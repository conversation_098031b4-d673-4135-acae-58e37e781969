{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "custom:water_dragon", "materials": {"default": "entity_alphatest"}, "textures": {"default": "textures/entity/waterdragon"}, "geometry": {"default": "geometry.dragon.water"}, "render_controllers": ["controller.render.water_dragon"], "animations": {"idle": "animation.dragon.idle", "walking": "animation.dragon.walking", "flying": "animation.dragon.flying", "tail-attack": "animation.dragon.tail-attack", "dragon-breath": "animation.dragon.dragon-breath", "bite-attack": "animation.dragon.bite-attack", "newbite": "animation.dragon.newbite", "transition-fly": "animation.dragon.transition-fly", "transition-tail-attack": "animation.dragon.transition-tail-attack", "transition-dragon-breath": "animation.dragon.transition-dragon-breath"}, "enable_attachables": false, "hide_armor": false}}}