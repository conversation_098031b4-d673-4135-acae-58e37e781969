package main

import (
	"anticheat"
	"anticheat/core"
	"anticheat/handlers"
	"github.com/ThronesMC/camera"
	"github.com/bedrock-gophers/intercept/intercept"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/chat"
	"github.com/df-mc/dragonfly/server/world"
	"log/slog"
	"path"
	"server/server"
	"server/server/command/base"
	"server/server/command/worldedit"
	"server/server/database"
	"server/server/entity/mobs"
	"server/server/entity/mobs/boss"
	"server/server/factions/listeners"
	"server/server/language"
	"server/server/schedulers"
	"server/server/user"
	"server/server/utils"

	_ "server/server/api"
)

func main() {
	chat.Global.Subscribe(chat.StdoutSubscriber{})
	log := slog.Default()

	schedulers.RegisterConfigurations()
	language.RegisterLanguages()
	schedulers.InitMongo(log)

	base.RegisterLobbyCommands()
	base.RegisterBuildCommands()
	base.RegisterFactionCommands()
	worldedit.RefreshBlocks()

	conf := utils.Panics(server.DefaultConfig().Config(log))
	conf.Entities = entity.DefaultRegistry.Config().New(utils.ConcatMultipleSlices[world.EntityType]([][]world.EntityType{
		{
			entity.AreaEffectCloudType,
			entity.ArrowType,
			entity.BottleOfEnchantingType,
			entity.EggType,
			entity.EnderPearlType,
			entity.ExperienceOrbType,
			entity.FallingBlockType,
			entity.FireworkType,
			entity.ItemType,
			entity.LightningType,
			entity.LingeringPotionType,
			entity.SnowballType,
			entity.SplashPotionType,
			entity.TNTType,
			entity.TextType,
		},
		{
			mobs.Zombie{},
			boss.Dragon{DragonIdentifier: "custom:dark_dragon"},
			boss.Dragon{DragonIdentifier: "custom:earth_dragon"},
			boss.Dragon{DragonIdentifier: "custom:fire_dragon"},
			boss.Dragon{DragonIdentifier: "custom:ice_dragon"},
			boss.Dragon{DragonIdentifier: "custom:light_dragon"},
			boss.Dragon{DragonIdentifier: "custom:water_dragon"},
		},
	}))
	conf.Listeners = intercept.WrapListeners(conf.Listeners)
	srv := conf.New()
	srv.CloseOnProgramEnd()
	srv.Listen()
	srv.World().SetTime(1200)
	srv.World().StopTime()
	srv.World().StopWeatherCycle()
	srv.World().StopRaining()
	srv.World().StopThundering()
	srv.World().Handle(listeners.WorldHandler{})
	server.MCServer = srv
	server.DragonflyConfig = conf  // Store the Dragonfly configuration globally
	anticheat.HookServer(srv, path.Join(".", "config", "anticheat.json"))
	intercept.Start(srv)

	go schedulers.ScheduleWorldSaving(conf)
	go schedulers.ScheduleItemsClearing()
	go schedulers.ScheduleDragonSpawning()
	go schedulers.ScheduleJackpotGrowth()

	schedulers.RefreshTextEntities()

	for pl := range srv.Accept() {
		camera.SendPresets(pl)

		anticheat.HookSession(pl)
		anticheat.SetPermissions(func(src cmd.Source) bool {
			return true
		}, func(src cmd.Source) bool {
			p, _ := src.(*player.Player)
			up := user.GetUser(p)
			return up.Data.Rank() <= database.Trainee
		})

		facHandler := &listeners.FactionHandler{}
		if (core.ACInvincible{}).Allow(pl) {
			facHandler.ACHandler = &handlers.ACPlayerHandler{}
		}
		pl.Handle(facHandler)

		listeners.HandlePlayerJoin(pl)
	}

	// Save all player data before shutdown
	for pl := range srv.Players(nil) {
		// Save Dragonfly player data (inventory, position, health, etc.)
		if err := conf.PlayerProvider.Save(pl.UUID(), pl.Data(), srv.World()); err != nil {
			errorCode := utils.RandString(6)
			log.With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save Dragonfly player data: " + err.Error())
		}

		// Save custom player data (faction data, stats, etc.)
		if err := user.Save(pl); err != nil {
			errorCode := utils.RandString(6)
			log.With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data: " + err.Error())
		}
	}

	// Save all cached data
	for identifier, err := range database.DB.SaveAll() {
		errorCode := utils.RandString(6)
		log.With("code", errorCode).With("identifier", identifier).Error(err.Error())
	}
}
