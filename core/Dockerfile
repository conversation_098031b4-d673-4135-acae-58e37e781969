# syntax=docker/dockerfile:1.4

FROM golang:1.25.0-alpine3.22 AS build

# Install ca-certificates and git
RUN apk add --no-cache ca-certificates git

# Set Go environment variables for better network handling
ENV GOPROXY=https://proxy.golang.org,direct
ENV GOSUMDB=sum.golang.org
ENV GOPRIVATE=""
ENV CGO_ENABLED=0

# Set the working directory to /build/core (not just /build)
WORKDIR /build

# Copy everything from production (context = production/)
COPY .. .

# Sync workspace with better error handling and timeout
RUN timeout 300 go work sync || echo "Workspace sync completed with warnings" && \
    ls -la

# Move to core folder and build
WORKDIR /build/core
RUN --mount=type=cache,id=gocache,target=/root/.cache/go-build \
    --mount=type=cache,id=gocache,target=/go/pkg/mod \
    go build -o /build/server-executable -v main.go


FROM alpine

RUN adduser -D -s /bin/sh -u 1000 server && \
    mkdir /server && \
    chown 1000:1000 /server

WORKDIR /server

COPY --chown=1000:1000 --from=build /build/server-executable .

EXPOSE 19132/udp

USER server

CMD ["/server/server-executable"]
