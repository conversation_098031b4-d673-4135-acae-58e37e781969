package database

import "github.com/sandertv/gophertunnel/minecraft/text"

type Rank int

const (
	Owner Rank = iota
	Admin
	Secretary
	Moderator
	Support
	Builder
	Trainee
	Youtuber
	MGP
	MLP
	MMP
	MVP
	VIP
	Player
)

var ShortenedRanks = []string{
	"owner", "admin", "secretary", "moderator", "support",
	"builder", "trainee", "youtuber", "mgp", "mlp",
	"mmp", "mvp", "vip", "player",
}

var RankPrefixes = []string{
	"<bold><aqua>OWNER</aqua></bold> ", "<bold><dark-aqua>ADMIN</dark-aqua></bold> ", "<bold><amethyst>SECRETARY</amethyst></bold> ", "<bold><green>MODERATOR</green></bold> ", "<bold><blue>SUPPORT</blue></bold> ",
	"<bold><black>BUILDER</black></bold> ", "<bold><white>TRAINEE</white></bold> ", "<bold><dark-red>YOU</dark-red><white>TUBER</white></bold> ", "<bold><red>MGP</red></bold> ", "<bold><dark-aqua>MLP</dark-aqua></bold> ",
	"<bold><gold>MMP</gold></bold> ", "<bold><yellow>MVP</yellow></bold> ", "<bold><green>VIP</green></bold> ", "",
}

func (r Rank) Shortened() string {
	return ShortenedRanks[r]
}

func (r Rank) Prefix() string {
	return text.Colourf(RankPrefixes[r])
}

func RankFromPrefix(prefix string) Rank {
	id := Owner
	for _, rank := range RankPrefixes {
		if text.Colourf(rank) == prefix {
			return id
		}
		id++
	}

	return -1
}

func RankFromName(name string) Rank {
	id := Owner
	for _, rank := range ShortenedRanks {
		if rank == name {
			return id
		}
		id++
	}

	return -1
}

type Role int

const (
	Member Role = iota
	CoLeader
	Leader
)

var RolePrefixes = []string{
	"<bold><grey>MEMBER</grey></bold> ",
	"<bold><white>CO-LEADER</white></bold> ",
	"<bold><white>LEADER</white></bold> ",
}
