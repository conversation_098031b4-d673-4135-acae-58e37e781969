package database

import (
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/debug"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/google/uuid"
	"image/color"
	"math"
	"server/server/utils"
	"time"
)

type FactionData struct {
	Name          string
	Members       []uuid.UUID
	Requests      []*Request
	ClaimArea     []*ClaimArea
	Allies        map[string]time.Time
	BankDoubloons float64
	Strength      float64
}

func (fac *FactionData) MaxChunksAllowed() int {
	return int(math.Round(fac.Strength))
}

func (fac *FactionData) RemainingChunks() int {
	allowed := fac.MaxChunksAllowed()
	used := len(fac.ClaimArea)
	remaining := allowed - used
	return remaining
}

func (fac *FactionData) HasClaim(ca *ClaimArea) bool {
	for _, existing := range fac.ClaimArea {
		if *existing == *ca {
			return true
		}
	}
	return false
}

func (fac *FactionData) RemoveClaim(ca *ClaimArea) {
	for i, existing := range fac.ClaimArea {
		if *existing == *ca {
			fac.ClaimArea = append(fac.ClaimArea[:i], fac.ClaimArea[i+1:]...)
			return
		}
	}
}

func (fac *FactionData) RefreshChunkLines(pl *player.Player) {
	s := utils.Session(pl)
	s.RemoveAllDebugShapes()
	for _, ca := range fac.ClaimArea {
		s.AddDebugShape(&debug.Box{
			Colour: color.RGBA{G: 255, A: 255},
			Bounds: mgl64.Vec3{16, 16, 16},
			Position: mgl64.Vec3{
				float64(ca[0] << 4),
				pl.Position().Y() - 8,
				float64(ca[1] << 4),
			},
		})
	}
	s.SendDebugShapes()
}

func FactionWithin(pos mgl64.Vec3) (res *FactionData) {
	DB.ForEachCachedFaction(func(fac *FactionData) bool {
		for _, ca := range fac.ClaimArea {
			if ca.Within(pos) {
				res = fac
				return false
			}
		}
		return true
	})
	return res
}

type RequestType int

const (
	JoinFaction RequestType = iota
	AllyFaction
)

type Request struct {
	SentBy            uuid.UUID
	SentAt            time.Time
	SenderFactionName string
	TargetFactionName string
	Type              RequestType
	Description       string
}

func (r *Request) LongType() string {
	if r.Type == JoinFaction {
		return "Join Request"
	}
	return "Alliance Request"
}

// ClaimArea represents one claimed chunk (X, Z)
type ClaimArea [2]int

func NewClaimArea(pos mgl64.Vec3) *ClaimArea {
	chunkX := int(pos.X()) >> 4
	chunkZ := int(pos.Z()) >> 4
	return &ClaimArea{chunkX, chunkZ}
}

func (ca *ClaimArea) Within(pos mgl64.Vec3) bool {
	x := int(pos.X()) >> 4
	z := int(pos.Z()) >> 4
	return x == ca[0] && z == ca[1]
}
