package database

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/item/enchantment"
	"github.com/df-mc/dragonfly/server/world"
	_ "unsafe"
)

type CustomStack struct {
	Id int32

	Name  string
	Meta  int16
	Count int

	CustomName string
	Lore       []string

	Damage int

	AnvilCost int

	Data map[string]any

	Enchantments []SerializedEnchantment
}

func NewCustomStack(src item.Stack) CustomStack {
	var name string
	var meta int16
	if src.Item() == nil {
		name, meta = block.Air{}.EncodeItem()
	} else {
		name, meta = src.Item().EncodeItem()
	}

	var enchantments []SerializedEnchantment
	for _, e := range src.Enchantments() {
		enchantments = append(enchantments, NewSerializedEnchantment(e))
	}

	return CustomStack{
		Name:         name,
		Meta:         meta,
		Count:        src.Count(),
		CustomName:   src.CustomName(),
		Lore:         src.Lore(),
		Damage:       src.MaxDurability() - src.Durability(),
		AnvilCost:    src.AnvilCost(),
		Data:         src.Values(),
		Enchantments: enchantments,
	}
}

func (c CustomStack) Stack() item.Stack {
	it, ok := world.ItemByName(c.Name, c.Meta)
	if !ok {
		return item.NewStack(block.Air{}, 1)
	}

	var enchantments []item.Enchantment
	for _, e := range c.Enchantments {
		if e.Level > 0 {
			enchantments = append(enchantments, e.Enchantment())
		}
	}

	dst := item.NewStack(it, c.Count).WithCustomName(c.CustomName).WithLore(c.Lore...).WithEnchantments(enchantments...)
	dst = dst.Damage(c.Damage)
	dst = dst.WithAnvilCost(c.AnvilCost)
	for k, v := range c.Data {
		dst = dst.WithValue(k, v)
	}

	return dst
}

type SerializedEnchantment struct {
	Name  string
	Level int
}

func NewSerializedEnchantment(enchantment item.Enchantment) SerializedEnchantment {
	return SerializedEnchantment{Name: enchantment.Type().Name(), Level: enchantment.Level()}
}

func (s SerializedEnchantment) Enchantment() item.Enchantment {
	for _, t := range item.Enchantments() {
		if t.Name() == s.Name {
			return item.NewEnchantment(t, s.Level)
		}
	}
	return item.NewEnchantment(enchantment.SoulSpeed, s.Level)
}
