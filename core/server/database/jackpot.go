package database

import (
	"sync"
	"time"
)

// JackpotData represents the vote crate jackpot system
type JackpotData struct {
	Amount      float64   `json:"amount" bson:"amount"`
	LastWinner  string    `json:"last_winner" bson:"last_winner"`
	LastWinTime time.Time `json:"last_win_time" bson:"last_win_time"`
	LastWinAmount float64 `json:"last_win_amount" bson:"last_win_amount"`
	TotalWins   int       `json:"total_wins" bson:"total_wins"`
	UpdatedAt   time.Time `json:"updated_at" bson:"updated_at"`
}

// Global jackpot instance with thread safety
var (
	currentJackpot = &JackpotData{
		Amount:    27332805, // Starting jackpot amount
		UpdatedAt: time.Now(),
	}
	jackpotMutex = sync.RWMutex{}
)

// GetJackpot returns the current jackpot amount thread-safely
func GetJackpot() *JackpotData {
	jackpotMutex.RLock()
	defer jackpotMutex.RUnlock()
	
	// Return a copy to prevent external modification
	return &JackpotData{
		Amount:        currentJackpot.Amount,
		LastWinner:    currentJackpot.LastWinner,
		LastWinTime:   currentJackpot.LastWinTime,
		LastWinAmount: currentJackpot.LastWinAmount,
		TotalWins:     currentJackpot.TotalWins,
		UpdatedAt:     currentJackpot.UpdatedAt,
	}
}

// AddToJackpot adds an amount to the jackpot thread-safely
func AddToJackpot(amount float64) {
	jackpotMutex.Lock()
	defer jackpotMutex.Unlock()
	
	currentJackpot.Amount += amount
	currentJackpot.UpdatedAt = time.Now()
	
	// Save to database asynchronously
	go func() {
		if err := DB.SaveJackpot(currentJackpot); err != nil {
			// Log error but don't block
		}
	}()
}

// WinJackpot handles a jackpot win, returns the won amount
func WinJackpot(winner string) float64 {
	jackpotMutex.Lock()
	defer jackpotMutex.Unlock()
	
	wonAmount := currentJackpot.Amount
	currentJackpot.LastWinner = winner
	currentJackpot.LastWinTime = time.Now()
	currentJackpot.LastWinAmount = wonAmount
	currentJackpot.TotalWins++
	currentJackpot.Amount = 1000000 // Reset to 1M base amount
	currentJackpot.UpdatedAt = time.Now()
	
	// Save to database asynchronously
	go func() {
		if err := DB.SaveJackpot(currentJackpot); err != nil {
			// Log error but don't block
		}
	}()
	
	return wonAmount
}

// LoadJackpotFromDatabase loads jackpot data from database
func LoadJackpotFromDatabase() error {
	jackpotData, err := DB.FindJackpot()
	if err != nil {
		return err
	}
	
	if jackpotData != nil {
		jackpotMutex.Lock()
		currentJackpot = jackpotData
		jackpotMutex.Unlock()
	}
	
	return nil
}
