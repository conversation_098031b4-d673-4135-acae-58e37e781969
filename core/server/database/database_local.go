package database

import (
	"go.mongodb.org/mongo-driver/mongo"
	"regexp"
	"server/server/utils"
	"strings"
	"sync"

	"github.com/google/uuid"
)

func NewLocalDatabase() *LocalDatabase {
	return &LocalDatabase{
		playerMap:  make(map[uuid.UUID]*PlayerData),
		factionMap: make(map[string]*FactionData),
	}
}

type LocalDatabase struct {
	playerMap   map[uuid.UUID]*PlayerData
	factionMap  map[string]*FactionData
	jackpotData *JackpotData
	mu          sync.RWMutex
}

func (d *LocalDatabase) Type() string {
	return "Local"
}

func (d *LocalDatabase) Client() *mongo.Client {
	return nil
}

func (d *LocalDatabase) CreatePlayer(data *PlayerData) error {
	d.mu.Lock()
	defer d.mu.Unlock()
	d.playerMap[data.UUID] = data
	return nil
}

func (d *LocalDatabase) SavePlayer(data *PlayerData) error {
	return d.CreatePlayer(data)
}

func (d *LocalDatabase) DeletePlayer(uuid uuid.UUID) error {
	player, err := d.FindPlayer(uuid)
	if err != nil {
		return err
	}

	d.mu.Lock()
	defer d.mu.Unlock()
	delete(d.playerMap, player.UUID)
	return nil
}

func (d *LocalDatabase) FindPlayer(uuid uuid.UUID) (*PlayerData, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()
	if d.playerMap[uuid] != nil {
		return d.playerMap[uuid], nil
	}
	return nil, utils.PlayerDataNotFoundError{Identifier: uuid.String()}
}

func (d *LocalDatabase) FindPlayerByName(playerName string) (*PlayerData, error) {
	patternStr := regexp.QuoteMeta(strings.ToLower(playerName))
	pattern, err := regexp.Compile("(?i)" + patternStr)
	if err != nil {
		return nil, err
	}

	d.mu.RLock()
	defer d.mu.RUnlock()

	for _, playerData := range d.playerMap {
		if pattern.MatchString(playerData.Username) {
			return playerData, nil
		}
	}
	return nil, utils.PlayerDataNotFoundError{Identifier: playerName}
}

func (d *LocalDatabase) CreateFaction(data *FactionData) error {
	d.mu.Lock()
	defer d.mu.Unlock()
	d.factionMap[data.Name] = data
	return nil
}

func (d *LocalDatabase) SaveFaction(data *FactionData) error {
	return d.CreateFaction(data)
}

func (d *LocalDatabase) DeleteFaction(name string) error {
	d.mu.Lock()
	defer d.mu.Unlock()
	if _, ok := d.factionMap[name]; !ok {
		return utils.FactionDataNotFoundError{Identifier: name}
	}
	delete(d.factionMap, name)
	return nil
}

func (d *LocalDatabase) FindFaction(name string) (*FactionData, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()
	if faction, ok := d.factionMap[name]; ok {
		return faction, nil
	}
	return nil, utils.FactionDataNotFoundError{Identifier: name}
}

func (d *LocalDatabase) CachedFactions() map[string]*FactionData {
	d.mu.RLock()
	defer d.mu.RUnlock()

	cpy := make(map[string]*FactionData, len(d.factionMap))
	for k, v := range d.factionMap {
		cpy[k] = v
	}
	return cpy
}

func (d *LocalDatabase) ForEachCachedFaction(fn func(*FactionData) bool) {
	d.mu.RLock()
	defer d.mu.RUnlock()
	for _, faction := range d.factionMap {
		if !fn(faction) {
			break
		}
	}
}

func (d *LocalDatabase) LoadAllFactionsIntoCache() error {
	return nil
}

func (d *LocalDatabase) SaveJackpot(data *JackpotData) error {
	d.mu.Lock()
	defer d.mu.Unlock()
	d.jackpotData = data
	return nil
}

func (d *LocalDatabase) FindJackpot() (*JackpotData, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return d.jackpotData, nil
}

func (d *LocalDatabase) SaveAll() map[string]error {
	return make(map[string]error) // noop
}
