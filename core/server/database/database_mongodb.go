package database

import (
	"context"
	"errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"regexp"
	"server/server/utils"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func NewMongoDBDatabase(uri string) (*MongoDBDatabase, error) {
	client, err := mongo.Connect(context.TODO(), options.Client().ApplyURI(uri))
	if err != nil {
		return nil, err
	}
	if err = client.Database("mmc").CreateCollection(context.TODO(), "player_data"); err != nil {
		return nil, err
	}
	if err = client.Database("mmc").CreateCollection(context.TODO(), "faction_data"); err != nil {
		return nil, err
	}
	if err = client.Database("mmc").CreateCollection(context.TODO(), "jackpot_data"); err != nil {
		return nil, err
	}

	mdb := &MongoDBDatabase{client: client, cache: NewLocalDatabase()}

	if err := mdb.LoadAllFactionsIntoCache(); err != nil {
		return nil, err
	}

	return mdb, nil
}

type MongoDBDatabase struct {
	client *mongo.Client
	cache  *LocalDatabase
}

func (*MongoDBDatabase) Type() string {
	return "MongoDB"
}

func (d *MongoDBDatabase) Client() *mongo.Client {
	return d.client
}

// -- PLAYER METHODS --

func (d *MongoDBDatabase) playerCollection() *mongo.Collection {
	return d.client.Database("mmc").Collection("player_data")
}

func (d *MongoDBDatabase) CreatePlayer(data *PlayerData) error {
	_, err := d.playerCollection().InsertOne(context.TODO(), data)
	if err != nil {
		return err
	}
	return d.cache.CreatePlayer(data)
}

func (d *MongoDBDatabase) SavePlayer(data *PlayerData) error {
	res := d.playerCollection().FindOneAndReplace(context.TODO(), bson.D{{"uuid", data.UUID}}, data)
	if res.Err() != nil {
		return res.Err()
	}
	return d.cache.SavePlayer(data)
}

func (d *MongoDBDatabase) DeletePlayer(uuid uuid.UUID) error {
	player, err := d.FindPlayer(uuid)
	if err != nil {
		return err
	}
	_, err = d.playerCollection().DeleteOne(context.TODO(), bson.D{{"uuid", player.UUID}})
	if err != nil {
		return err
	}
	return d.cache.DeletePlayer(uuid)
}

func (d *MongoDBDatabase) FindPlayer(uuid uuid.UUID) (*PlayerData, error) {
	if data, err := d.cache.FindPlayer(uuid); err == nil {
		return data, nil
	}
	return d.findPlayerFromQuery(bson.D{{"uuid", uuid}}, uuid.String())
}

func (d *MongoDBDatabase) FindPlayerByName(name string) (*PlayerData, error) {
	if data, err := d.cache.FindPlayerByName(name); err == nil {
		return data, nil
	}

	filter := bson.D{
		{"username", primitive.Regex{
			Pattern: regexp.QuoteMeta(name),
			Options: "i",
		}},
	}

	return d.findPlayerFromQuery(filter, name)
}

func (d *MongoDBDatabase) findPlayerFromQuery(query bson.D, identifier string) (*PlayerData, error) {
	result := d.playerCollection().FindOne(context.TODO(), query)
	if result.Err() != nil {
		if errors.Is(result.Err(), mongo.ErrNoDocuments) {
			return nil, utils.PlayerDataNotFoundError{Identifier: identifier}
		}
		return nil, result.Err()
	}
	var data *PlayerData
	if err := result.Decode(&data); err != nil {
		return nil, err
	}
	return data, nil
}

// -- FACTION METHODS --

func (d *MongoDBDatabase) factionCollection() *mongo.Collection {
	return d.client.Database("mmc").Collection("faction_data")
}

func (d *MongoDBDatabase) CreateFaction(data *FactionData) error {
	_, err := d.factionCollection().InsertOne(context.TODO(), data)
	if err != nil {
		return err
	}
	return d.cache.CreateFaction(data)
}

func (d *MongoDBDatabase) SaveFaction(data *FactionData) error {
	res := d.factionCollection().FindOneAndReplace(context.TODO(), bson.D{{"name", data.Name}}, data)
	if res.Err() != nil {
		return res.Err()
	}
	return d.cache.SaveFaction(data)
}

func (d *MongoDBDatabase) DeleteFaction(name string) error {
	_, err := d.factionCollection().DeleteOne(context.TODO(), bson.D{{"name", name}})
	if err != nil {
		return err
	}
	return d.cache.DeleteFaction(name)
}

func (d *MongoDBDatabase) FindFaction(name string) (*FactionData, error) {
	if data, err := d.cache.FindFaction(name); err == nil {
		return data, nil
	}
	return d.findFactionFromQuery(bson.D{{"name", name}}, name)
}

func (d *MongoDBDatabase) findFactionFromQuery(query bson.D, identifier string) (*FactionData, error) {
	result := d.factionCollection().FindOne(context.TODO(), query)
	if result.Err() != nil {
		if errors.Is(result.Err(), mongo.ErrNoDocuments) {
			return nil, utils.FactionDataNotFoundError{Identifier: identifier}
		}
		return nil, result.Err()
	}
	var data FactionData
	if err := result.Decode(&data); err != nil {
		return nil, err
	}
	_ = d.cache.CreateFaction(&data)
	return &data, nil
}

func (d *MongoDBDatabase) CachedFactions() map[string]*FactionData {
	return d.cache.CachedFactions()
}

func (d *MongoDBDatabase) ForEachCachedFaction(fn func(*FactionData) bool) {
	d.cache.ForEachCachedFaction(fn)
}

func (d *MongoDBDatabase) LoadAllFactionsIntoCache() error {
	cursor, err := d.factionCollection().Find(context.TODO(), bson.D{})
	if err != nil {
		return err
	}
	defer cursor.Close(context.TODO())

	var factions []*FactionData
	if err := cursor.All(context.TODO(), &factions); err != nil {
		return err
	}

	d.cache.mu.Lock()
	defer d.cache.mu.Unlock()
	for _, faction := range factions {
		d.cache.factionMap[faction.Name] = faction
	}
	return nil
}

// -- JACKPOT METHODS --

func (d *MongoDBDatabase) jackpotCollection() *mongo.Collection {
	return d.client.Database("mmc").Collection("jackpot_data")
}

func (d *MongoDBDatabase) SaveJackpot(data *JackpotData) error {
	filter := bson.D{{"_id", "vote_jackpot"}}
	update := bson.D{{"$set", data}}
	opts := options.Update().SetUpsert(true)

	_, err := d.jackpotCollection().UpdateOne(context.TODO(), filter, update, opts)
	return err
}

func (d *MongoDBDatabase) FindJackpot() (*JackpotData, error) {
	result := d.jackpotCollection().FindOne(context.TODO(), bson.D{{"_id", "vote_jackpot"}})
	if result.Err() != nil {
		if errors.Is(result.Err(), mongo.ErrNoDocuments) {
			return nil, nil // No jackpot data found, will use default
		}
		return nil, result.Err()
	}

	var data JackpotData
	if err := result.Decode(&data); err != nil {
		return nil, err
	}
	return &data, nil
}

func (d *MongoDBDatabase) SaveAll() map[string]error {
	errs := make(map[string]error)

	for _, data := range d.cache.playerMap {
		err := d.SavePlayer(data)
		if err != nil {
			errs[data.Username] = err
		}
	}
	for _, data := range d.cache.factionMap {
		err := d.SaveFaction(data)
		if err != nil {
			errs[data.Name] = err
		}
	}
	return errs
}
