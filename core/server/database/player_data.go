package database

import (
	"github.com/df-mc/dragonfly/server/item"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/google/uuid"
	"server/server/utils"
	"time"
)

type PlayerData struct {
	UUID     uuid.UUID
	Username string

	Online     bool
	FirstLogin time.Time
	LastLogin  time.Time
	ProtocolId string
	RankId     string

	Faction PlayerFaction
}

func (d *PlayerData) Rank() Rank {
	return RankFromName(d.RankId)
}

type PlayerFaction struct {
	Stats    Stats
	Home     map[string]mgl64.Vec3
	Backpack map[int]map[int]CustomStack
	Bounties map[uuid.UUID]float64
	Request  *Request

	Name        string
	Role        Role
	FirstJoined time.Time
}

func (f PlayerFaction) HasFaction() bool {
	return f.Name != ""
}

func (f PlayerFaction) Faction() *FactionData {
	if f.HasFaction() {
		return utils.Panics(DB.FindFaction(f.Name))
	}
	return nil
}

func (f PlayerFaction) DeserializedBackpack() map[int]map[int]item.Stack {
	var dst map[int]map[int]item.Stack
	for page, m := range f.Backpack {
		for slot, cStack := range m {
			dst[page][slot] = cStack.Stack()
		}
	}
	return dst
}

func (f PlayerFaction) TotalBounties() (sum float64) {
	for _, b := range f.Bounties {
		sum += b
	}
	return sum
}

type Stats struct {
	Doubloons      float64
	Strength       float64
	Kills          int
	KillStreak     int
	BestKillStreak int
	Deaths         int
	CrateKeys      map[CrateType]int
	Kits           map[Rank]time.Time
}

func (s Stats) KDR() float64 {
	if s.Deaths == 0 {
		return 0
	}
	return float64(s.Kills) / float64(s.Deaths)
}
