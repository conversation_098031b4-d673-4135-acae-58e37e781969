package utils

import (
    "encoding/json"
    "fmt"
    "github.com/go-gl/mathgl/mgl64"
    "github.com/sandertv/gophertunnel/minecraft/text"
    "math"
    "math/rand"
    "os"
    "reflect"
)

func ReadConfig[T any](file string) (T, error) {
    var zero T
    if _, err := os.Stat(file); os.IsNotExist(err) {
        data, err := json.Marshal(zero)
        if err != nil {
            return zero, fmt.<PERSON><PERSON><PERSON>("encode default config: %v", err)
        }
        if err := os.WriteFile(file, data, 0644); err != nil {
            return zero, fmt.Errorf("create default config: %v", err)
        }
        return zero, err
    }
    data, err := os.ReadFile(file)
    if err != nil {
        return zero, fmt.<PERSON><PERSON><PERSON>("read config: %v", err)
    }
    if err := json.Unmarshal(data, &zero); err != nil {
        return zero, fmt.<PERSON><PERSON><PERSON>("decode config: %v", err)
    }
    return zero, err
}

func Filter[T any](slice []T, callable func(v T) bool) []T {
    var newSlice []T

    for _, v := range slice {
        if callable(v) {
            newSlice = append(newSlice, v)
        }
    }
    return newSlice
}

func ConcatMultipleSlices[T any](slices [][]T) []T {
    var totalLen int

    for _, s := range slices {
        totalLen += len(s)
    }

    result := make([]T, totalLen)

    var i int

    for _, s := range slices {
        i += copy(result[i:], s)
    }

    return result
}

func GetNestedField(obj interface{}, keys []string) interface{} {
    val := reflect.ValueOf(obj)
    for _, key := range keys {
        if val.Kind() == reflect.Ptr {
            val = val.Elem()
        }

        if val.Kind() == reflect.Struct {
            val = val.FieldByName(key)
            if !val.IsValid() {
                return nil
            }
        } else {
            return nil
        }
    }
    return val.Interface()
}

func RandChance(chance float64) bool {
    return chance > rand.Float64()*100
}

const charset = "abcdefghijklmnopqrstuvwxyz" +
    "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

func RandString(length int) string {
    b := make([]byte, length)
    for i := range b {
        b[i] = charset[rand.Intn(len(charset))]
    }
    return string(b)
}

func IntToRoman(num int) string {
    roman := ""
    numbers := []int{1, 4, 5, 9, 10, 40, 50, 90, 100, 400, 500, 900, 1000}
    romans := []string{"I", "IV", "V", "IX", "X", "XL", "L", "XC", "C", "CD", "D", "CM", "M"}
    index := len(romans) - 1

    for num > 0 {
        for numbers[index] <= num {
            roman += romans[index]
            num -= numbers[index]
        }
        index -= 1
    }

    return roman
}

func Rad2deg(rad float64) float64 {
    return rad * (180 / math.Pi)
}

func ShortenNumber(n float64, dp int) string {
    units := []string{"", text.Colourf("<yellow>K</yellow>"), text.Colourf("<dark-red>M</dark-red>"), text.Colourf("<purple>B</purple>"), text.Colourf("<black>T</black>"), text.Colourf("<red>QD</red>"), text.Colourf("<dark-red>QT</dark-red>")}
    num := math.Round(n*math.Pow10(dp)) / math.Pow10(dp)
    for i := 0; i < len(units); i++ {
        if num >= 1000 {
            num /= 1000
        } else {
            if dp == 0 {
                return fmt.Sprintf("%.0f%s", num, units[i])
            } else {
                return fmt.Sprintf("%.1f%s", num, units[i])
            }
        }
    }
    if dp == 0 {
        return fmt.Sprintf("%.0f%s", num, units[len(units)-1])
    } else {
        return fmt.Sprintf("%.1f%s", num, units[len(units)-1])
    }
}

func SetVecY(vec mgl64.Vec3, value float64) mgl64.Vec3 {
    return mgl64.Vec3{vec.X(), value, vec.Z()}
}

func AddCommas(n float64) string {
    // Check if it's a whole number
    if n == float64(int64(n)) {
        // Convert to int64 to avoid .0 formatting issues
        str := fmt.Sprintf("%d", int64(n))
        if len(str) <= 3 {
            return str
        }

        var result []byte
        for i, digit := range []byte(str) {
            if i > 0 && (len(str)-i)%3 == 0 {
                result = append(result, ',')
            }
            result = append(result, digit)
        }
        return string(result)
    } else {
        // Handle decimal numbers
        str := fmt.Sprintf("%.2f", n)
        if len(str) <= 3 {
            return str
        }

        var result []byte
        for i, digit := range []byte(str) {
            if i > 0 && (len(str)-i)%3 == 0 {
                result = append(result, ',')
            }
            result = append(result, digit)
        }
        return string(result)
    }
}
