package utils

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/item/inventory"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/session"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/protocol"
	"github.com/sandertv/gophertunnel/minecraft/protocol/packet"
	"reflect"
	"unsafe"
	_ "unsafe"
)

// UpdatePrivateField sets a private field of a session to the value passed. Credits to bedrock-goephers/unsafe
func UpdatePrivateField[T any](v any, name string, value T) {
	reflectedValue := reflect.ValueOf(v).Elem()
	privateFieldValue := reflectedValue.FieldByName(name)

	privateFieldValue = reflect.NewAt(privateFieldValue.Type(), unsafe.Pointer(privateFieldValue.UnsafeAddr())).Elem()

	privateFieldValue.Set(reflect.ValueOf(value))
}

// FetchPrivateField fetches a private field of a session. Credits to bedrock-goephers/unsafe
func FetchPrivateField[T any](s any, name string) T {
	reflectedValue := reflect.ValueOf(s).Elem()
	privateFieldValue := reflectedValue.FieldByName(name)
	privateFieldValue = reflect.NewAt(privateFieldValue.Type(), unsafe.Pointer(privateFieldValue.UnsafeAddr())).Elem()

	return privateFieldValue.Interface().(T)
}

//go:linkname Session github.com/df-mc/dragonfly/server/player.(*Player).session
func Session(_ *player.Player) *session.Session

//go:linkname WritePacket github.com/df-mc/dragonfly/server/session.(*Session).writePacket
func WritePacket(_ *session.Session, _ packet.Packet)

//go:linkname EntityRuntimeID github.com/df-mc/dragonfly/server/session.(*Session).entityRuntimeID
func EntityRuntimeID(_ *session.Session, _ world.Entity) uint64

//go:linkname FirstReplaceable github.com/df-mc/dragonfly/server/block.firstReplaceable
func FirstReplaceable(_ *world.Tx, _ cube.Pos, _ cube.Face, _ world.Block) (cube.Pos, cube.Face, bool)

//go:linkname Place github.com/df-mc/dragonfly/server/block.place
func Place(_ *world.Tx, _ cube.Pos, _ world.Block, _ item.User, _ *item.UseContext)

//go:linkname ParseEntityMetadata github.com/df-mc/dragonfly/server/session.(*Session).parseEntityMetadata
func ParseEntityMetadata(_ *session.Session, _ world.Entity) protocol.EntityMetadata

//go:linkname NextWindowId github.com/df-mc/dragonfly/server/session.(*Session).nextWindowID
func NextWindowId(_ *session.Session) byte

//go:linkname SendInv github.com/df-mc/dragonfly/server/session.(*Session).sendInv
func SendInv(_ *session.Session, _ *inventory.Inventory, _ uint32)

//go:linkname SendItem github.com/df-mc/dragonfly/server/session.(*Session).sendItem
func SendItem(_ *session.Session, _ item.Stack, _ int, _ uint32)
