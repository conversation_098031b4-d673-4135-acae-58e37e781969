package utils

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/block/model"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl32"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/protocol/packet"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
	"math"
	"strings"
)

func Distance(v1 mgl64.Vec3, v2 mgl64.Vec3) float64 {
	return math.Sqrt(math.Pow(v2.X()-v1.X(), 2) + math.Pow(v2.Y()-v1.Y(), 2) + math.Pow(v2.Z()-v1.Z(), 2))
}

func NearestPlayer(l *living.Living, tx *world.Tx) (minP *player.Player, minD float64) {
	for e := range tx.Players() {
		if pl, ok := e.(*player.Player); ok && pl.XUID() != "" {
			if d := Distance(l.Position(), pl.Position()); minP == nil || d < minD {
				minP = pl
				minD = d
			}
		}
	}
	return minP, minD
}

// DragonMoveToTarget Only for dragon.
func DragonMoveToTarget(l *living.Living, tx *world.Tx, t mgl64.Vec3) {
	if !l.AttackImmune() {
		currLoc := l.Position()
		speed := l.Speed()

		x := t.X() - currLoc.X()
		y := t.Y() - currLoc.Y()
		z := t.Z() - currLoc.Z()

		xzSq := x*x + z*z
		xzModulus := math.Sqrt(xzSq)

		if xzSq < 2 {
			l.SetVelocity(mgl64.Vec3{0, l.Velocity().Y(), 0})
		} else {
			speedFactor := speed * 0.15
			l.SetVelocity(mgl64.Vec3{speedFactor * (x / xzModulus), y * 0.01, speedFactor * (z / xzModulus)})
		}

		vec := l.Position().Add(l.Rotation().Vec3())
		newV := mgl64.Vec3{vec.X(), l.Position().Y(), vec.Z()}
		block := tx.Block(cube.PosFromVec3(newV))
		if block != nil {
			if _, ok := block.Model().(model.Empty); !ok && l.OnGround() {
				l.SetVelocity(mgl64.Vec3{l.Velocity().X(), 0.1, l.Velocity().Z()})
			}
		}

		l.Move(mgl64.Vec3{l.Velocity().X(), l.Velocity().Y(), l.Velocity().Z()}, Rad2deg(math.Atan2(-x, z))-l.Rotation().Yaw(), Rad2deg(-math.Atan2(y, xzModulus))-l.Rotation().Pitch(), tx)
	}
}

func HopToTarget(l *living.Living, jumpVelocity float64, t mgl64.Vec3, tx *world.Tx) {
	if !l.AttackImmune() {
		l.SetVelocity(l.Velocity().Add(mgl64.Vec3{0, -jumpVelocity, 0}))
		currLoc := l.Position()
		speed := l.Speed()

		x := t.X() - currLoc.X()
		y := t.Y() - currLoc.Y()
		z := t.Z() - currLoc.Z()

		xzSq := x*x + z*z
		xzModulus := math.Sqrt(xzSq)

		if xzSq < 2 {
			l.SetVelocity(mgl64.Vec3{0, l.Velocity().Y(), 0})
		} else {
			speedFactor := speed * 0.15
			l.SetVelocity(mgl64.Vec3{speedFactor * (x / xzModulus), -jumpVelocity, speedFactor * (z / xzModulus)})
		}

		l.Move(mgl64.Vec3{l.Velocity().X(), jumpVelocity, l.Velocity().Z()}, Rad2deg(math.Atan2(-x, z)), Rad2deg(-math.Atan2(y, xzModulus)), tx)
		if l.OnGround() {
			l.Move(mgl64.Vec3{l.Velocity().X(), l.Velocity().Y(), l.Velocity().Z()}, 0, 0, tx)
		}
	}
}

func SpawnParticle(pl *player.Player, position mgl64.Vec3, particle string) {
	WritePacket(Session(pl), &packet.SpawnParticleEffect{
		EntityUniqueID: -1,
		Position:       mgl32.Vec3{float32(position.X()), float32(position.Y()), float32(position.Z())},
		ParticleName:   particle,
	})
}

func ItemDisplay(it item.Stack) string {
	if it.CustomName() != "" {
		return it.CustomName()
	}
	name, _ := it.Item().EncodeItem()
	name = strings.Split(name, ":")[1]
	name = strings.ReplaceAll(name, "-", " ")
	name = strings.ReplaceAll(name, "_", " ")
	name = cases.Title(language.English).String(name)
	return name
}
