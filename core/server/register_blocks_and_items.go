package server

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/item/creative"
	"github.com/df-mc/dragonfly/server/world"
	"server/server/blocks/heads/chicken"
	"server/server/blocks/heads/enderman"
	"server/server/blocks/heads/pillager"
	"server/server/blocks/heads/polar_bear"
	"server/server/blocks/heads/witch"
	"server/server/blocks/vanilla"
)

func init() {
	creative.RegisterGroup(creative.Group{Category: creative.NatureCategory(), Name: "Custom", Icon: item.NewStack(block.Emerald{}, 1)})

	for _, direction := range cube.Directions() {
		world.RegisterBlock(chicken.Head{Facing: direction})
		world.RegisterBlock(enderman.Head{Facing: direction})
		world.RegisterBlock(pillager.Head{Facing: direction})
		world.RegisterBlock(polar_bear.Head{Facing: direction})
		world.RegisterBlock(witch.Head{Facing: direction})
	}
	RegisterItem(chicken.Head{})
	RegisterItem(enderman.Head{})
	RegisterItem(pillager.Head{})
	RegisterItem(polar_bear.Head{})
	RegisterItem(witch.Head{})

	world.RegisterBlock(vanilla.Ice{})
	RegisterItem(vanilla.Ice{})

	world.RegisterBlock(vanilla.Magma{})
	RegisterItem(vanilla.Magma{})

	world.RegisterBlock(vanilla.Slime{})
	RegisterItem(vanilla.Slime{})

	for _, b := range vanilla.AllSnowLayers() {
		world.RegisterBlock(b)
	}
	RegisterItem(vanilla.SnowLayer{})

	RegisterItem(vanilla.ChestMineCart{})
}

func RegisterItem(i world.Item) {
	world.RegisterItem(i)
	creative.RegisterItem(creative.Item{Stack: item.NewStack(i, 1), Group: "Custom"})
}
