package api

import (
	"github.com/gin-gonic/gin"
	"server/server/database"
	"server/server/user"
	"server/server/utils"
)

func initPostRequests(rg *gin.RouterGroup) {
	rg.POST("/players/update", jwtAuthMiddleware(), func(c *gin.Context) {
		var pd *database.PlayerData
		utils.Panic(c.BindJ<PERSON>(pd))

		user.UpdateUserData(pd)
		utils.Panic(database.DB.SavePlayer(pd))
	})

	rg.POST("/factions/update", jwtAuthMiddleware(), func(c *gin.Context) {
		var fd *database.FactionData
		utils.Panic(c.BindJSON(fd))

		utils.Panic(database.DB.SaveFaction(fd))
	})
}
