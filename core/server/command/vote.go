package command

import (
	"encoding/json"
	"fmt"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"io/ioutil"
	"net/http"
	"net/url"
	"server/server"
	"server/server/database"
	"server/server/user"
	"strings"
	"time"
)

type VoteCommand struct {
}

type voteResponse struct {
	Status    int    `json:"status"`
	Error     string `json:"error"`
	VoteCount int    `json:"vote_count"`
}

func (p VoteCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		playerName := pl.Name()

		_, status, err := checkVoteStatus(playerName)
		if err != nil {
			o.Error(text.Colourf("<red>Error checking vote status: %v</red>", err))
			return
		}

		switch status {
		case 0:
			pl.Message(text.Colourf("<red>You haven't voted in the last 24 hours!</red>\n<yellow>Visit: massacremc.net/vote </yellow>\n<yellow>Make sure to use your exact username: %s</yellow>", playerName))
		case 1: // voted but not claimed
			u.Data.Faction.Stats.CrateKeys[database.Vote]++
			err := claimVote(playerName)
			if err != nil {
				pl.Message(text.Colourf("<red>Error claiming vote reward: %v</red>", err))
				return
			}

			pl.Message(text.Colourf("%v <green>Thank you for voting! You received <green>1</green> vote key. You can vote again in 24 hours!</green>", server.Config.Prefix))
		case 2: // Has voted and already claimed
			pl.Message(text.Colourf("%v <yellow>You've already claimed your vote reward for the last 24 hours.</yellow>", server.Config.Prefix))
			pl.Message(text.Colourf("<yellow>You can vote again in 24 hours from your last vote.</yellow>"))
		default:
			pl.Message(text.Colourf("<red>Unexpected response from vote API: %d</red>", status))
		}
	} else {
		pl.Message(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

func checkVoteStatus(username string) (bool, int, error) {
	encodedUsername := url.QueryEscape(strings.ToLower(username))

	apiURL := fmt.Sprintf("https://minecraftpocket-servers.com/api/?object=votes&element=claim&key=%s&username=%s",
		server.Config.VoteAPIKey, encodedUsername)

	client := &http.Client{Timeout: 10 * time.Second}

	resp, err := client.Get(apiURL)
	if err != nil {
		return false, 0, fmt.Errorf("failed to connect to vote API: %v", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return false, 0, fmt.Errorf("failed to read API response: %v", err)
	}

	var jsonResponse voteResponse
	if err := json.Unmarshal(body, &jsonResponse); err == nil {
		return jsonResponse.Status > 0, jsonResponse.Status, nil
	}

	responseText := strings.TrimSpace(string(body))
	switch responseText {
	case "0":
		return false, 0, nil
	case "1":
		return true, 1, nil
	case "2":
		return true, 2, nil
	default:
		return false, 0, fmt.Errorf("unexpected API response: %s", responseText)
	}
}

func claimVote(username string) error {
	encodedUsername := url.QueryEscape(strings.ToLower(username))

	apiURL := fmt.Sprintf("https://minecraftpocket-servers.com/api/?action=post&object=votes&element=claim&key=%s&username=%s",
		server.Config.VoteAPIKey, encodedUsername)

	client := &http.Client{Timeout: 10 * time.Second}

	resp, err := client.Get(apiURL)
	if err != nil {
		return fmt.Errorf("failed to claim vote: %v", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read claim response: %v", err)
	}

	responseText := strings.TrimSpace(string(body))
	if responseText != "1" {
		return fmt.Errorf("failed to claim vote, response: %s", responseText)
	}

	return nil
}
