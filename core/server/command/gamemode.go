package command

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/language"
	"strings"
)

type GameModeCommand struct {
	Targets  []cmd.Target `cmd:"target"`
	GameMode GameModeType `cmd:"gamemode"`
}

func (GameModeCommand) Allow(src cmd.Source) bool {
	return GameMode.Test(src)
}

func (GameModeCommand) PermissionMessage(src cmd.Source) string {
	return GameMode.PermissionMessage(src)
}

func (g GameModeCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		if len(g.Targets) != 1 {
			o.Error(text.Colourf(language.Translate(pl).Commands.Target))
			return
		}
		t := g.Targets[0].(*player.Player)
		switch g.GameMode {
		case "survival":
			t.<PERSON>Game<PERSON>ode(world.GameModeSurvival)
		case "creative":
			t.<PERSON>Game<PERSON>ode(world.GameModeCreative)
		case "adventure":
			t.SetGameMode(world.GameModeAdventure)
		case "spectator":
			t.SetGameMode(world.GameModeSpectator)
		}
		pl.Message(text.Colourf(language.Translate(pl).Commands.GameMode.Success, server.Config.Prefix, strings.ToTitle(string(g.GameMode))))
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type GameModeType string

func (GameModeType) Type() string {
	return "gamemode"
}

func (GameModeType) Options(_ cmd.Source) []string {
	return []string{"survival", "creative", "adventure", "spectator"}
}
