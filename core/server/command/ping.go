package command

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/language"
	"time"
)

type PingCommand struct {
	Target cmd.Optional[[]cmd.Target] `cmd:"target"`
}

func (p PingCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		var target cmd.Target
		if targets, ok := p.Target.Load(); ok {
			if len(targets) != 1 {
				o.Error(text.Colourf(language.Translate(pl).Commands.Target))
				return
			}
			target = targets[0]
		} else {
			target = src
		}
		pl := target.(*player.Player)
		targetStr := "Your"
		if src != target {
			targetStr = text.Colourf("<yellow>%v's</yellow>", pl.Name())
		}

		o.Print(text.Colourf(language.Translate(pl).Commands.Ping.Success))
		o.Print(text.Colourf("%v<diamond>%v ping is <yellow>%v</yellow>ms.</diamond>", server.Config.Prefix, targetStr, pl.Latency().Round(time.Millisecond).Milliseconds()))
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}
