package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/command"
	"server/server/language"
	"server/server/user"
	"time"
)

type FlyCommand struct{}

func (FlyCommand) Allow(src cmd.Source) bool {
	return command.Fly.Test(src)
}

func (FlyCommand) PermissionMessage(src cmd.Source) string {
	return command.Fly.PermissionMessage(src)
}

func (FlyCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		if u.IsCoolDownActive(user.Combat, 15*time.Second, false, false, true) {
			return
		}

		if pl.Flying() {
			pl.StopFlying()
			pl.SetGameMode(world.GameModeSurvival)
			o.Print(text.Colourf(language.Translate(pl).Commands.Fly.Success, server.Config.Prefix, "disabled"))
		} else {
			pl.SetGameMode(user.FlightGameMode{})
			pl.StartFlying()
			o.Print(text.Colourf(language.Translate(pl).Commands.Fly.Success, server.Config.Prefix, "enabled"))
		}

	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}
