package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/language"
	"server/server/user"
	"server/server/utils"
)

type PayCommand struct {
	Targets []cmd.Target `cmd:"target"`
	Amount  float64      `cmd:"amount"`
}

func (p PayCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		if p.Amount <= 0 {
			o.Error(text.Colourf("<red>Amount must be greater than 0!</red>"))
			return
		}

		if len(p.Targets) != 1 {
			o.Error(text.Colourf(language.Translate(pl).Commands.Target))
			return
		}
		t := p.Targets[0].(*player.Player)
		ut := user.GetUser(t)
		u := user.GetUser(pl)

		if t.UUID() == pl.UUID() {
			o.Error(text.Colourf(language.Translate(pl).Commands.Pay.CannotPaySelf))
			return
		}

		if u.Data.Faction.Stats.Doubloons >= p.Amount {
			u.Data.Faction.Stats.Doubloons -= p.Amount
			o.Print(text.Colourf(language.Translate(pl).Commands.Pay.PaySuccess, server.Config.Prefix, user.FactionNameDisplay.Name(ut.Data), utils.ShortenNumber(p.Amount, 0)))
			ut.Data.Faction.Stats.Doubloons += p.Amount
			t.Message(text.Colourf(language.Translate(t).Commands.Pay.ObtainSuccess, server.Config.Prefix, user.FactionNameDisplay.Name(u.Data), utils.ShortenNumber(p.Amount, 0)))
		} else {
			o.Error(text.Colourf(language.Translate(pl).Commands.Bank.NotEnoughDoubloons))
		}
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}
