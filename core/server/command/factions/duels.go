package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/title"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"golang.org/x/exp/maps"
	"math/rand"
	"server/server"
	"server/server/language"
	"server/server/user"
	"time"
)

type DuelInviteCommand struct {
	Invite        cmd.SubCommand `cmd:"invite"`
	Targets       []cmd.Target   `cmd:"target"`
	KeepInventory bool           `cmd:"keep inventory"`
}

func (d DuelInviteCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		if u.IsCoolDownActive(user.Combat, 15*time.Second, false, false, true) {
			return
		}

		if len(d.Targets) != 1 {
			o.Error(text.Colourf(language.Translate(pl).Commands.Target))
			return
		}
		t := d.Targets[0].(*player.Player)
		ut := user.GetUser(t)

		if t.UUID() == pl.UUID() {
			o.Error(text.Colourf(language.Translate(pl).Commands.Duel.CannotInviteSelf))
			return
		}

		ut.FactionInfo.DuelInvites[pl] = d.KeepInventory
		go func() {
			time.Sleep(time.Minute)
			delete(ut.FactionInfo.DuelInvites, pl)
			pl.Message(text.Colourf(language.Translate(pl).Commands.Duel.InviteExpired, server.Config.Prefix, user.FactionNameDisplay.Name(u.Data)))
		}()
		t.Message(text.Colourf(language.Translate(t).Commands.Duel.InviteSuccess, server.Config.Prefix, d.KeepInventory, user.FactionNameDisplay.Name(u.Data)))
		o.Print(text.Colourf(language.Translate(pl).Commands.Duel.InviteSent, server.Config.Prefix))

	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type DuelAcceptCommand struct {
	Accept  cmd.SubCommand           `cmd:"accept"`
	Targets cmd.Optional[cmd.Target] `cmd:"target"`
}

func (d DuelAcceptCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		var t *player.Player
		if target, ok := d.Targets.Load(); ok {
			t = target.(*player.Player)
		} else {
			if duelRequests := maps.Keys[map[*player.Player]bool, *player.Player, bool](u.FactionInfo.DuelInvites); len(duelRequests) > 0 {
				t = duelRequests[0]
			}
		}
		if t != nil {
			ut := user.GetUser(t)

			x := -4500 + rand.Intn(9000)
			z := -4500 + rand.Intn(9000)
			y := tx.HighestBlock(x, z) + 3

			pl.Teleport(mgl64.Vec3{float64(x), float64(y), float64(z)})
			t.Teleport(mgl64.Vec3{float64(x + 10), float64(y), float64(z)})

			pl.SetImmobile()
			t.SetImmobile()

			go func() {
				countdown := []string{"<dark-green>5</dark-green>", "<green>4</green>", "<yellow>3</yellow>", "<gold>2</gold>", "<red>1</red>"}
				for _, cd := range countdown {
					pl.SendTitle(title.New(text.Colourf(cd)))
					t.SendTitle(title.New(text.Colourf(cd)))
					time.Sleep(time.Second)
				}
				pl.SendTitle(title.New(text.Colourf("<bold><dark-red>FIGHT</dark-red></bold>")))
				t.SendTitle(title.New(text.Colourf("<bold><dark-red>FIGHT</dark-red></bold>")))
				pl.SetMobile()
				t.SetMobile()
				u.IsCoolDownActive(user.Combat, 15*time.Second, true, true, false)
				ut.IsCoolDownActive(user.Combat, 15*time.Second, true, true, false)
			}()
		} else {
			o.Error(text.Colourf(language.Translate(pl).Commands.Duel.NoRequester))
		}
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}
