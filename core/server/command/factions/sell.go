package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/language"
	"server/server/user"
	"server/server/utils"
)

type SellHandCommand struct {
	Hand cmd.SubCommand `cmd:"hand"`
}

func (s SellHandCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		main, off := pl.HeldItems()
		if buyable := ItemFinder(main); buyable != nil {
			p := ItemFinder(main).BuyPrice * float64(main.Count())
			if p != 0 {
				u := user.GetUser(pl)
				u.Data.Faction.Stats.Doubloons += p
				pl.SetHeldItems(main.Grow(-64), off)

				o.Print(text.Colourf(language.Translate(pl).Commands.Sell.Success, server.Config.Prefix, p))
			} else {
				o.Error(text.Colourf(language.Translate(pl).Commands.Sell.CannotSell))
			}
		} else {
			o.Error(text.Colourf(language.Translate(pl).Commands.Sell.CannotSell))
		}
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type SellAllCommand struct {
	All cmd.SubCommand `cmd:"all"`
}

func (s SellAllCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		f := true
		var totalIncome float64
		for slot := 0; slot < 36; slot++ {
			it, err := pl.Inventory().Item(slot)
			if err != nil {
				panic(err)
			}
			buyable := ItemFinder(it)
			if buyable != nil {
				p := buyable.BuyPrice * float64(it.Count())
				if p != 0 {
					u := user.GetUser(pl)
					u.Data.Faction.Stats.Doubloons += p
					totalIncome += p

					if stack, err := pl.Inventory().Item(slot); err == nil {
						if err := pl.Inventory().SetItem(slot, stack.Grow(-64)); err != nil {
							panic(err)
						}
						f = false
					}
				}
			}
		}
		if f {
			o.Error(text.Colourf(language.Translate(pl).Commands.Sell.CannotSell))
		} else {
			o.Print(text.Colourf(language.Translate(pl).Commands.Sell.Success, server.Config.Prefix, utils.ShortenNumber(totalIncome, 2)))
		}
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}
