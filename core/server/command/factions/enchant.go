package factions

import (
	"github.com/bedrock-gophers/inv/inv"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/event"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/item/inventory"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/dragonfly/server/world/sound"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server/blocks"
	"server/server/factions/enchants"
	"server/server/language"
	"server/server/ui"
	"server/server/utils"
	"slices"
)

type EnchantCommand struct{}

func (e EnchantCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		sess := utils.Session(pl)
		chestInv := inventory.New(inv.ContainerChest{}.Size(), func(slot int, before, after item.Stack) {
			sess.ViewSlotChange(slot, after)
		})
		menu := inv.NewCustomMenu(text.Colourf("<lapis>Enchanter</lapis>"), inv.ContainerChest{}, chestInv, func(inv *inventory.Inventory) {
			it1, _ := chestInv.Item(10)
			it2, _ := chestInv.Item(12)

			_, _ = pl.Inventory().AddItem(it1)
			_, _ = pl.Inventory().AddItem(it2)
		})

		enchant := func() {
			pl.SetExperienceLevel(pl.ExperienceLevel() - 20)
			pl.PlaySound(sound.LevelUp{})
			it1, _ := chestInv.Item(10)
			it2, _ := chestInv.Item(12)

			utils.Panic(chestInv.SetItem(10, it1.Grow(-1)))
			utils.Panic(chestInv.SetItem(12, it2.Grow(-1)))
		}

		chestInv.Handle(ui.ChestUIHandler{
			Inventory: chestInv,
			Functions: []func(ctx *event.Context[inventory.Holder], slot int, stack item.Stack, inv *inventory.Inventory){
				// take
				func(ctx *event.Context[inventory.Holder], slot int, stack item.Stack, inv *inventory.Inventory) {
					if stackValue, ok := stack.Value("enchanter"); ok {
						v := stackValue.(string)
						if v == "glass" || v == "sign" {
							ctx.Cancel()
						} else if sufficient, ok := stack.Value("sufficient"); ok && v == "anvil" {
							ctx.Cancel()
							if sufficient.(bool) {
								h := pl.H()
								go func() {
									if output, _ := chestInv.Item(16); output.Count() != 0 {
										h.ExecWorld(func(tx *world.Tx, e world.Entity) {
											_, _ = pl.Inventory().AddItem(output)
										})
										utils.Panic(chestInv.SetItem(16, output.Grow(-1)))
										enchant()
									} else {
										pl.PlaySound(sound.Deny{})
										pl.Message(text.Colourf(language.Translate(pl).Error.NotCompatible))
									}
								}()
							} else {
								pl.PlaySound(sound.Deny{})
								pl.Message(text.Colourf(language.Translate(pl).Error.NotEnoughExperience))
							}
						}
					} else if slot == 16 && pl.Experience() >= 20 {
						go enchant()
					} else if slot == 10 || slot == 12 {
						go func() {
							if output, _ := chestInv.Item(16); output.Count() != 0 {
								utils.Panic(chestInv.SetItem(16, output.Grow(-1)))
							}
						}()
					}
				},

				//place
				func(ctx *event.Context[inventory.Holder], slot int, stack item.Stack, inv *inventory.Inventory) {
					if slot == 10 || slot == 12 {
						go func() {
							it1, _ := chestInv.Item(10)
							it2, _ := chestInv.Item(12)

							if slot == 10 {
								it1 = stack
							} else if slot == 12 {
								it2 = stack
							}

							if output, ok := mergeEnchantments([]item.Stack{it1, it2}); ok {
								utils.Panic(chestInv.SetItem(16, *output))
								pl.PlaySound(sound.AnvilUse{})
							}
						}()
					} else {
						ctx.Cancel()
					}
				},

				//drop
				func(ctx *event.Context[inventory.Holder], slot int, stack item.Stack, inv *inventory.Inventory) {
					ctx.Cancel()
				},
			},
		})

		for i := 0; i < 27; i++ {
			if i == 10 || i == 12 || i == 16 {
				continue
			}
			utils.Panic(chestInv.SetItem(i, item.NewStack(block.StainedGlassPane{Colour: item.ColourPurple()}, 1).WithCustomName(" ").WithValue("index", i).WithValue("enchanter", "glass")))
		}

		utils.Panic(chestInv.SetItem(18, item.NewStack(block.Sign{Wood: block.OakWood()}, 1).WithCustomName(text.Colourf("<yellow>Add 2 items on the 2 empty slots on the left to merge their enchantments into one!</yellow>")).WithValue("enchanter", "sign")))
		anvil := item.NewStack(block.Anvil{Type: block.UndamagedAnvil()}, 1).WithValue("enchanter", "anvil")
		if pl.ExperienceLevel() >= 20 {
			anvil = anvil.WithCustomName(text.Colourf("<dark-green>Click to enchant your item <dark-red>[20 XP]</dark-red></dark-green>")).WithValue("sufficient", true)
		} else {
			anvil = anvil.WithCustomName(text.Colourf("<red>You must have 20 XP to enchant the items</red>")).WithValue("sufficient", false)
		}
		utils.Panic(chestInv.SetItem(14, anvil))

		inv.SendMenu(pl, menu)
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

func mergeEnchantments(its []item.Stack) (*item.Stack, bool) {
	var toolType *item.ToolType
	toolTier := -1
	mergedEnchantments := make(map[item.EnchantmentType]int)

	for _, it := range its {
		if _, isEbook := it.Item().(item.EnchantedBook); !isEbook {
			if t, ok := it.Item().(item.Tool); ok {
				if toolType == nil {
					temp := t.ToolType()
					toolType = &temp
				} else if t.ToolType() != *toolType {
					return nil, false
				}
			}

			if toolTier == -1 {
				toolTier = TierId(it.Item())
			} else if TierId(it.Item()) != toolTier {
				return nil, false
			}
		}

		for _, enchant := range it.Enchantments() {
			if _, ok := enchant.Type().(enchants.Glitter); ok {
				continue
			}
			if mergedEnchantments[enchant.Type()] != 0 {
				if mergedEnchantments[enchant.Type()] == enchant.Level() {
					mergedEnchantments[enchant.Type()] = enchant.Level() + 1
				} else {
					return nil, false
				}
			} else {
				mergedEnchantments[enchant.Type()] = enchant.Level()
			}
		}
	}

	var outputIt item.Stack

	if itOutput, ok := ToolTypeToItem(toolType, toolTier); ok {
		outputIt = item.NewStack(itOutput, 1)
	} else {
		return nil, false
	}

	for t, lvl := range mergedEnchantments {
		for _, it := range its {
			if _, ok := it.Item().(item.EnchantedBook); !ok && !t.CompatibleWithItem(it.Item()) {
				return nil, false
			}
		}
		outputIt = blocks.AddEnchantmentLore(outputIt.WithEnchantments(item.NewEnchantment(t, lvl)))
	}

	if len(mergedEnchantments) > 0 && len(utils.Filter(its, func(v item.Stack) bool {
		return v.Count() != 0
	})) > 1 {
		return &outputIt, true
	}

	return nil, false
}

func TierId(it world.Item) int {
	if _, ok := it.(item.Bow); ok {
		return -2
	}

	if i, ok := it.(item.Sword); ok {
		return slices.Index(item.ToolTiers(), i.Tier)
	}

	if i, ok := it.(item.Axe); ok {
		return slices.Index(item.ToolTiers(), i.Tier)
	}

	if i, ok := it.(item.Pickaxe); ok {
		return slices.Index(item.ToolTiers(), i.Tier)
	}

	if i, ok := it.(item.Shovel); ok {
		return slices.Index(item.ToolTiers(), i.Tier)
	}

	if i, ok := it.(item.Hoe); ok {
		return slices.Index(item.ToolTiers(), i.Tier)
	}

	return -1
}

func ToolTypeToItem(toolType *item.ToolType, tierId int) (world.Item, bool) {
	if toolType == nil {
		if tierId == -2 {
			return item.Bow{}, true
		}
		return item.Bow{}, false
	}

	if tierId == -1 {
		return item.EnchantedBook{}, true
	}

	tid := item.ToolTiers()[tierId]

	if *toolType == item.TypeSword {
		return item.Sword{Tier: tid}, true
	} else if *toolType == item.TypeAxe {
		return item.Axe{Tier: tid}, true
	} else if *toolType == item.TypePickaxe {
		return item.Pickaxe{Tier: tid}, true
	} else if *toolType == item.TypeShovel {
		return item.Shovel{Tier: tid}, true
	} else if *toolType == item.TypeHoe {
		return item.Hoe{Tier: tid}, true
	}

	return item.Bow{}, false
}
