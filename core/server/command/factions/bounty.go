package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/language"
	"server/server/user"
	"server/server/utils"
	"time"
)

type BountyCommand struct {
	Target []cmd.Target `cmd:"target"`
	Amount float64      `cmd:"amount"`
}

func (b BountyCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		if u.IsCoolDownActive(user.Combat, 15*time.Second, false, false, true) {
			return
		}

		if b.Amount <= 0 {
			o.Error(text.Colourf("<red>Bounty amount must be greater than 0!</red>"))
			return
		}

		if len(b.Target) != 1 {
			o.Error(text.Colourf(language.Translate(pl).Commands.Target))
			return
		}
		if target, ok := b.Target[0].(*player.Player); ok {
			if target.UUID() == pl.UUID() {
				o.Error(text.Colourf("<red>You cannot place a bounty on yourself!</red>"))
				return
			}

			u := user.GetUser(pl)
			dubs := u.Data.Faction.Stats.Doubloons
			ut := user.GetUser(target)
			if dubs > b.Amount {
				u.Data.Faction.Stats.Doubloons -= b.Amount
				if _, ok := ut.Data.Faction.Bounties[pl.UUID()]; ok {
					ut.Data.Faction.Bounties[pl.UUID()] += b.Amount
				} else {
					ut.Data.Faction.Bounties[pl.UUID()] = b.Amount
				}
				pl.Message(text.Colourf(language.Translate(pl).Commands.Bounty.Success, server.Config.Prefix, b.Amount, user.FactionNameDisplay.Name(ut.Data)))
			} else {
				pl.Message(text.Colourf(language.Translate(pl).Commands.Bounty.Error, utils.ShortenNumber(b.Amount, 2)))
			}
		}
	} else {
		o.Error(text.Colourf("%v<red>You cannot use this command in console. Please execute it in-game.</red>", server.Config.Prefix))
	}
}
