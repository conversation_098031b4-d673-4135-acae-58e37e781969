package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server/command"
	"server/server/language"
	"server/server/user"
	"time"
)

type RepairHandCommand struct {
	Hand cmd.SubCommand `cmd:"hand"`
}

func (r RepairHandCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		if u.IsCoolDownActive(user.Combat, 15*time.Second, false, false, true) {
			return
		}

		main, off := pl.HeldItems()
		pl.SetHeldItems(main.WithDurability(main.MaxDurability()), off.WithDurability(off.MaxDurability()))
		o.Print(text.Colourf(language.Translate(pl).Whoosh))

	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type RepairAllCommand struct {
	All cmd.SubCommand `cmd:"all"`
}

func (RepairAllCommand) Allow(src cmd.Source) bool {
	return command.RepairAll.Test(src)
}

func (RepairAllCommand) PermissionMessage(src cmd.Source) string {
	return command.RepairAll.PermissionMessage(src)
}

func (r RepairAllCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		if u.IsCoolDownActive(user.Combat, 15*time.Second, false, false, true) {
			return
		}

		for slot, it := range pl.Armour().Inventory().Items() {
			if err := pl.Armour().Inventory().SetItem(slot, it.WithDurability(it.MaxDurability())); err != nil {
				panic(err)
			}
		}
		for slot, it := range pl.Inventory().Items() {
			if err := pl.Inventory().SetItem(slot, it.WithDurability(it.MaxDurability())); err != nil {
				panic(err)
			}
		}
		o.Print(text.Colourf(language.Translate(pl).Whoosh))

	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}
