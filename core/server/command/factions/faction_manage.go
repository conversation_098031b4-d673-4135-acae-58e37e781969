package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/form"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/google/uuid"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"golang.org/x/exp/maps"
	"server/server"
	"server/server/database"
	"server/server/factions/items"
	"server/server/language"
	"server/server/ui"
	"server/server/user"
	"server/server/utils"
	"slices"
	"strings"
	"time"
)

type FactionManageCommand struct{}

func (FactionManageCommand) Run(src cmd.Source, _ *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		FactionManageUI{}.SendTo(pl)
	}
}

type FactionDepositCommand struct {
	Deposit cmd.SubCommand `cmd:"deposit"`
	Amount  float64        `cmd:"amount"`
}

func (f FactionDepositCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		if u.Data.Faction.HasFaction() {
			if f.Amount <= 0 {
				o.Error(text.Colourf("<red>Amount must be greater than 0!</red>"))
				return
			}
			if d := u.Data.Faction.Stats.Doubloons; d >= f.Amount {
				u.Data.Faction.Stats.Doubloons -= f.Amount
				u.Data.Faction.Faction().BankDoubloons += f.Amount
				utils.Panic(database.DB.SaveFaction(u.Data.Faction.Faction()))
				o.Print(text.Colourf("<green>Successfully deposited %v doubloons to faction bank!</green>", utils.ShortenNumber(f.Amount, 2)))
			} else {
				o.Error(text.Colourf("<red>You don't have enough doubloons! You have %v but need %v.</red>", utils.ShortenNumber(d, 2), utils.ShortenNumber(f.Amount, 2)))
			}
		} else {
			o.Error(text.Colourf("<red>You must be in a faction to use this command!</red>"))
		}
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type FactionWithdrawCommand struct {
	Withdraw cmd.SubCommand `cmd:"withdraw"`
	Amount   float64        `cmd:"amount"`
}

func (f FactionWithdrawCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		if u := user.GetUser(pl); u.Data.Faction.HasFaction() {
			if f.Amount <= 0 {
				o.Error(text.Colourf("<red>Amount must be greater than 0!</red>"))
				return
			}
			fac := u.Data.Faction.Faction()
			if fac.BankDoubloons >= f.Amount {
				fac.BankDoubloons -= f.Amount
				utils.Panic(database.DB.SaveFaction(fac))
				if _, err := pl.Inventory().AddItem(items.BankNote{Amount: f.Amount}.Stack()); err != nil {
					pl.Drop(items.BankNote{Amount: f.Amount}.Stack())
					pl.Message(text.Colourf("<yellow>Inventory full! Bank note dropped on the ground.</yellow>"))
				}
				o.Print(text.Colourf("<green>Successfully withdrew %v doubloons from faction bank!</green>", utils.ShortenNumber(f.Amount, 2)))
			} else {
				o.Error(text.Colourf("<red>Faction bank doesn't have enough doubloons! Bank has %v but you requested %v.</red>", utils.ShortenNumber(fac.BankDoubloons, 2), utils.ShortenNumber(f.Amount, 2)))
			}
		} else {
			o.Error(text.Colourf("<red>You must be in a faction to use this command!</red>"))
		}
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type FactionBalanceCommand struct {
	Balance cmd.SubCommand `cmd:"balance"`
}

func (f FactionBalanceCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		if u := user.GetUser(pl); u.Data.Faction.HasFaction() {
			fac := u.Data.Faction.Faction()
			o.Print(text.Colourf("<green>Faction Bank Balance: <yellow>%v</yellow> doubloons</green>", utils.AddCommas(fac.BankDoubloons)))
		} else {
			o.Error(text.Colourf("<red>You must be in a faction to use this command!</red>"))
		}
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type FactionManageUI struct{}

func (f FactionManageUI) Submit(submitter form.Submitter, button form.Button, tx *world.Tx) {
	pl := submitter.(*player.Player)
	u := user.GetUser(pl)
	switch button.Text {
	case text.Colourf("<aqua>Create a Faction</aqua>"):
		FactionCreateUI{}.SendTo(pl)
	case text.Colourf("<aqua>Allies</aqua>"):
		FactionAlliesUI{}.SendTo(pl)
	case text.Colourf("<aqua>Members</aqua>"):
		FactionMembersUI{}.SendTo(pl)
	case text.Colourf("<aqua>Apply to Join Faction</aqua>"):
		FactionApplyUI{}.SendTo(pl)
	case text.Colourf("<aqua>Request Alliance</aqua>"):
		FactionAllianceUI{}.SendTo(pl)
	case text.Colourf("<aqua>View Requests</aqua>"):
		FactionRequestsUI{}.SendTo(pl)
	case text.Colourf("<aqua>View Claim Area</aqua>"):
		FactionClaimUI{}.SendTo(pl)
	case text.Colourf("<bold><red>Disband Faction</red></bold>"):
		u.Data.Faction.Role = database.Member

		n := u.Data.Faction.Name
		fac := utils.Panics(database.DB.FindFaction(n))

		for ally := range fac.Allies {
			allyFac := utils.Panics(database.DB.FindFaction(ally))
			var newAllysAllies map[string]time.Time

			//filter
			for a, joinAt := range allyFac.Allies {
				if a != ally {
					newAllysAllies[a] = joinAt
				}
			}

			allyFac.Allies = newAllysAllies
			utils.Panic(database.DB.SaveFaction(allyFac))
		}

		for _, id := range fac.Members {
			d := user.GetUserByUUID(id).Data
			d.Faction.Name = ""

			if e, ok := server.MCServer.PlayerByName(d.Username); ok {
				if ent, ok := e.Entity(tx); ok {
					ent.(*player.Player).SetNameTag(user.FactionNameDisplay.Name(d))
				} else {
					e.ExecWorld(func(tx *world.Tx, e world.Entity) {
						ent.(*player.Player).SetNameTag(user.FactionNameDisplay.Name(d))
					})
				}
			}
		}

		utils.Panic(database.DB.DeleteFaction(n))

		pl.SetNameTag(user.FactionNameDisplay.Name(u.Data))
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Disband, server.Config.Prefix))
	case text.Colourf("<bold><red>Leave Faction</red></bold>"):
		u.Data.Faction.Role = database.Member

		var newFacMembers []uuid.UUID
		for _, id := range u.Data.Faction.Faction().Members {
			if id != pl.UUID() {
				newFacMembers = append(newFacMembers, id)
			}
		}
		fac := u.Data.Faction.Faction()
		fac.Members = newFacMembers
		utils.Panic(database.DB.SaveFaction(fac))

		u.Data.Faction.Name = ""
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Leave, server.Config.Prefix))
	}
}

func (f FactionManageUI) SendTo(pl *player.Player) {
	u := user.GetUser(pl)
	fm := form.NewMenu(FactionManageUI{}, text.Colourf("<aqua>Faction Management</aqua>"))
	if u.Data.Faction.HasFaction() {
		fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<aqua>Allies</aqua>"), "", ""))
		fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<aqua>Members</aqua>"), "", ""))

		if u.Data.Faction.Role == database.Leader || u.Data.Faction.Role == database.CoLeader {
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<aqua>Request Alliance</aqua>"), "", ""))
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<aqua>View Requests</aqua>"), "", ""))
		}

		if u.Data.Faction.Role == database.Leader {
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<aqua>View Claim Area</aqua>"), "", ""))
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<red>Disband Faction</red>"), "", ""))
		} else {
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<bold><red>Leave Faction</red></bold>"), "", ""))
		}
	} else {
		fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<aqua>Create a Faction</aqua>"), "", ""))
		fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<aqua>Apply to Join Faction</aqua>"), "", ""))
	}
	pl.SendForm(fm)
}

type FactionCreateUI struct {
	FactionName form.Input
}

func (f FactionCreateUI) Submit(submitter form.Submitter, _ *world.Tx) {
	pl := submitter.(*player.Player)
	n := f.FactionName.Value()
	if len(n) < 2 || len(n) > 14 {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Create.Error.BadLength))
		return
	}

	// Check for section symbol which is used for color codes
	if strings.Contains(n, "\u00A7") {
		pl.Message(text.Colourf("<red>Faction names cannot contain color codes!</red>"))
		return
	}

	if _, err := database.DB.FindFaction(n); err == nil {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Create.Error.ExistingName))
		return
	}

	utils.Panic(database.DB.CreateFaction(&database.FactionData{
		Name:    n,
		Allies:  map[string]time.Time{},
		Members: []uuid.UUID{pl.UUID()},
	}))

	u := user.GetUser(pl)
	u.Data.Faction.Name = n
	u.Data.Faction.Role = database.Leader
	u.Data.Faction.FirstJoined = time.Now()

	pl.SetNameTag(user.FactionNameDisplay.Name(u.Data))
	pl.Message(text.Colourf(language.Translate(pl).FactionManage.Create.Success, server.Config.Prefix, n))
}

func (f FactionCreateUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionManageUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionCreateUI) SendTo(pl *player.Player) {
	fm := form.New(
		FactionCreateUI{
			FactionName: form.NewInput("Faction Name", "", "Enter a faction name (2-14 characters)"),
		}, text.Colourf("<aqua>Create Your Faction</aqua>"))
	pl.SendForm(fm)
}

type FactionAlliesUI struct{}

func (f FactionAlliesUI) Submit(submitter form.Submitter, button form.Button, _ *world.Tx) {
	pl := submitter.(*player.Player)
	FactionAllyUI{}.SendTo(pl, utils.Panics(database.DB.FindFaction(ui.Load[string](pl, button.Text))))
}

func (f FactionAlliesUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionManageUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionAlliesUI) SendTo(pl *player.Player) {
	u := user.GetUser(pl)
	fm := form.NewMenu(FactionAlliesUI{}, text.Colourf("<aqua>Faction Allies</aqua>"))
	allies := u.Data.Faction.Faction().Allies
	if len(allies) == 0 {
		fm = fm.WithBody(text.Colourf("<white>You have no allies.</white>"))
	} else {
		for name := range allies {
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<dark-grey>%v</dark-grey>", name), "", name))
		}
	}
	pl.SendForm(fm)
}

type FactionAllyUI struct {
	Back form.Button
}

func (f FactionAllyUI) Submit(submitter form.Submitter, _ form.Button, _ *world.Tx) {
	FactionAlliesUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionAllyUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionAlliesUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionAllyUI) SendTo(pl *player.Player, fac *database.FactionData) {
	fm := form.NewMenu(FactionAllyUI{
		Back: ui.AddButtonWithValue(pl, text.Colourf("<dark-grey>Back</dark-grey>"), "", ""),
	}, text.Colourf("<green>Ally Info</green>"))
	fm = fm.WithBody(text.Colourf(
		"<green>Name: <yellow>%v</yellow>\n"+
			"Owner: <yellow>%v</yellow>\n"+
			"Allies: <yellow>%v</yellow>\n"+
			"Total members: <yellow>%v</yellow>\n"+
			"Total strength: <yellow>%v</yellow></green>",
		fac.Name,
		user.FactionNameDisplay.Name(user.GetUserByUUID(fac.Members[0]).Data),
		strings.Join(maps.Keys(fac.Allies)[:], ", "),
		len(fac.Members),
		fac.Strength,
	))
	pl.SendForm(fm)
}

type FactionMembersUI struct{}

func (f FactionMembersUI) Submit(submitter form.Submitter, button form.Button, _ *world.Tx) {
	pl := submitter.(*player.Player)
	FactionMemberUI{}.SendTo(pl, ui.Load[*user.User](pl, button.Text))
}

func (f FactionMembersUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionManageUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionMembersUI) SendTo(pl *player.Player) {
	u := user.GetUser(pl)
	fm := form.NewMenu(FactionMembersUI{}, text.Colourf("<aqua>Faction Members</aqua>"))
	for _, id := range u.Data.Faction.Faction().Members {
		um := user.GetUserByUUID(id)
		fm = fm.WithButtons(ui.AddButtonWithValue(pl, user.FactionMemberName(um.Data), "", um))
	}
	pl.SendForm(fm)
}

type FactionMemberUI struct {
	member *user.User
}

func NewFactionMemberUI(member *user.User) FactionMemberUI {
	return FactionMemberUI{member: member}
}

func (f FactionMemberUI) Submit(submitter form.Submitter, button form.Button, _ *world.Tx) {
	pl := submitter.(*player.Player)
	switch ui.Load[string](pl, button.Text) {
	case "kick":
		fac := utils.Panics(database.DB.FindFaction(f.member.Data.Faction.Name))
		var newMembers []uuid.UUID
		for _, id := range fac.Members {
			if id != f.member.Player().UUID() {
				newMembers = append(newMembers, id)
			}
		}
		fac.Members = newMembers
		utils.Panic(database.DB.SaveFaction(fac))

		f.member.Data.Faction.Name = ""
		pl.Message(language.Translate(pl).FactionManage.Members.Kick, server.Config.Prefix, user.FactionNameDisplay.Name(f.member.Data))
	case "role":
		NewFactionMemberRoleUI(f.member).SendTo(pl)
	case "back":
		FactionMembersUI{}.SendTo(submitter.(*player.Player))
	}
}

func (f FactionMemberUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionMembersUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionMemberUI) SendTo(pl *player.Player, member *user.User) {
	u := user.GetUser(pl)
	fm := form.NewMenu(NewFactionMemberUI(member), text.Colourf("<aqua>Member Info</aqua>"))
	if u.Data.Faction.Faction().Members[0] == pl.UUID() && member.Player().UUID() != pl.UUID() || u.Data.Faction.Role < member.Data.Faction.Role {
		fm = fm.WithButtons(
			ui.AddButtonWithValue(pl, text.Colourf("<red>Kick</red>"), "", "kick"),
			ui.AddButtonWithValue(pl, text.Colourf("<aqua>Role</aqua>"), "", "role"),
		)
	}
	fm = fm.WithButtons(
		ui.AddButtonWithValue(pl, text.Colourf("<aqua>Back</aqua>"), "", "back"),
	)
	fm = fm.WithBody(text.Colourf(
		"<green>Name: <yellow>%v</yellow>\n"+
			"Joined at: <yellow>%v</yellow>\n"+
			"Role: <white>%v</white>\n"+
			"Doubloons: <yellow>%v</yellow>\n"+
			"Strength: <yellow>%v</yellow></green>",
		member.Data.Username,
		member.Data.Faction.FirstJoined.Format("Jan 2, 2006 at 3:04 PM"),
		getFactionRoleName(member.Data.Faction.Role),
		utils.AddCommas(member.Data.Faction.Stats.Doubloons),
		utils.ShortenNumber(member.Data.Faction.Stats.Strength, 0),
	))
	pl.SendForm(fm)
}

type FactionMemberRoleUI struct {
	member *user.User

	Role form.Dropdown
}

func NewFactionMemberRoleUI(member *user.User) FactionMemberRoleUI {
	return FactionMemberRoleUI{
		member: member,
		Role:   form.NewDropdown("Role", database.RolePrefixes[1:], int(member.Data.Faction.Role)),
	}
}

func (f FactionMemberRoleUI) Submit(submitter form.Submitter, _ *world.Tx) {
	pl := submitter.(*player.Player)
	f.member.Data.Faction.Role = database.Role(f.Role.Value())
	pl.Message(language.Translate(pl).FactionManage.Members.Role, server.Config.Prefix, user.FactionNameDisplay.Name(f.member.Data), database.RolePrefixes[f.Role.Value()])
}

func (f FactionMemberRoleUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionMemberUI{}.SendTo(submitter.(*player.Player), f.member)
}

func (f FactionMemberRoleUI) SendTo(pl *player.Player) {
	fm := form.New(NewFactionMemberRoleUI(f.member), text.Colourf("<aqua>Change %v's role</aqua>", user.FactionNameDisplay.Name(f.member.Data)))
	pl.SendForm(fm)
}

type FactionApplyUI struct {
	FactionName      form.Input
	ApplyDescription form.Input
}

func (f FactionApplyUI) Submit(submitter form.Submitter, _ *world.Tx) {
	pl := submitter.(*player.Player)
	u := user.GetUser(pl)
	n := f.FactionName.Value()
	d := f.ApplyDescription.Value()
	if d == "" {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.EmptyDescription))
		return
	}
	if len(d) < 10 {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.ShortDescription))
		return
	}
	if n == "" {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.EmptyFactionName))
		return
	}

	// Check for section symbol in faction name input
	if strings.Contains(n, "\u00A7") {
		pl.Message(text.Colourf("<red>Faction names cannot contain color codes!</red>"))
		return
	}

	fac, err := database.DB.FindFaction(n)
	if err != nil {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.FactionNotExist, n))
		return
	}
	if u.Data.Faction.HasFaction() {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.AlreadyInFaction))
		return
	}
	if u.Data.Faction.Request != nil {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.RequestReplaced, u.Data.Faction.Request.TargetFactionName))
	}

	r := &database.Request{
		SentBy:            pl.UUID(),
		SentAt:            time.Now(),
		SenderFactionName: "",
		TargetFactionName: n,
		Type:              database.JoinFaction,
		Description:       d,
	}

	u.Data.Faction.Request = r
	fac.Requests = append(fac.Requests, r)
	pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Success, server.Config.Prefix))
}

func (f FactionApplyUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionManageUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionApplyUI) SendTo(pl *player.Player) {
	fm := form.New(FactionApplyUI{
		FactionName:      form.NewInput("Faction Name", "", "Enter the faction name you want to join"),
		ApplyDescription: form.NewInput("Application Message", "", "Why do you want to join? (min 10 characters)"),
	}, text.Colourf("<aqua>Apply to Join Faction</aqua>"))
	pl.SendForm(fm)
}

type FactionAllianceUI struct {
	FactionName     form.Input
	AllyDescription form.Input
}

func (f FactionAllianceUI) Submit(submitter form.Submitter, _ *world.Tx) {
	pl := submitter.(*player.Player)
	u := user.GetUser(pl)
	n := f.FactionName.Value()
	d := f.AllyDescription.Value()
	if d == "" {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.EmptyDescription))
		return
	}
	if len(d) < 10 {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.EmptyDescription))
		return
	}
	if n == "" {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.EmptyFactionName))
		return
	}

	// Check for section symbol in faction name input
	if strings.Contains(n, "\u00A7") {
		pl.Message(text.Colourf("<red>Faction names cannot contain color codes!</red>"))
		return
	}

	fac, err := database.DB.FindFaction(n)
	if err != nil {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.FactionNotExist, n))
		return
	}
	if fac.Name == u.Data.Faction.Name {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.SameFaction))
		return
	}

	if u.Data.Faction.Request != nil {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.RequestReplaced))
	}

	r := &database.Request{
		SentBy:            pl.UUID(),
		SentAt:            time.Now(),
		SenderFactionName: u.Data.Faction.Name,
		TargetFactionName: n,
		Type:              database.AllyFaction,
		Description:       d,
	}

	u.Data.Faction.Request = r
	fac.Requests = append(fac.Requests, r)
	pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Success, server.Config.Prefix))
}

func (f FactionAllianceUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionManageUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionAllianceUI) SendTo(pl *player.Player) {
	fm := form.New(FactionAllianceUI{
		FactionName:     form.NewInput("Target Faction", "", "faction you want to ally with"),
		AllyDescription: form.NewInput("Alliance Proposal", "", "Why should they ally with you?"),
	}, text.Colourf("<aqua>Request Alliance</aqua>"))
	pl.SendForm(fm)
}

type FactionRequestsUI struct{}

func (f FactionRequestsUI) Submit(submitter form.Submitter, button form.Button, _ *world.Tx) {
	pl := submitter.(*player.Player)
	FactionRequestUI{}.SendTo(pl, ui.Load[*database.Request](pl, button.Text))
}

func (f FactionRequestsUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionManageUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionRequestsUI) SendTo(pl *player.Player) {
	u := user.GetUser(pl)
	fm := form.NewMenu(FactionRequestsUI{}, text.Colourf("<aqua>Faction Requests</aqua>"))
	reqSlice := u.Data.Faction.Faction().Requests
	if len(reqSlice) == 0 {
		fm = fm.WithBody(text.Colourf("<white>There are currently no requests.</white>"))
	} else {
		slices.Reverse(reqSlice)
		for _, r := range reqSlice {
			str := ""
			if r.SenderFactionName != "" {
				str = text.Colourf(" from <grey>%v</grey>", r.SenderFactionName)
			}
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<green>%v%v\n<italic><grey>Click for more information</grey></italic></green>", r.LongType(), str), "", r))
		}
	}
	pl.SendForm(fm)
}

type FactionRequestUI struct {
	request *database.Request
}

func NewFactionRequestUI(r *database.Request) FactionRequestUI {
	return FactionRequestUI{request: r}
}

func (f FactionRequestUI) Submit(submitter form.Submitter, button form.Button, _ *world.Tx) {
	pl := submitter.(*player.Player)
	u := user.GetUser(pl)
	ut := user.GetUserByUUID(f.request.SentBy)
	value := ui.Load[string](pl, button.Text)

	if value != "back" {
		fac := u.Data.Faction.Faction()
		if ut != nil {
			if value == "accept" {
				switch f.request.Type {
				case database.JoinFaction:
					fac.Members = append(fac.Members)
					pd := user.GetUserByUUID(f.request.SentBy).Data
					pd.Faction.Name = fac.Name
					pd.Faction.Role = database.Member
					pd.Faction.FirstJoined = time.Now()
				case database.AllyFaction:
					senderFac := utils.Panics(database.DB.FindFaction(f.request.SenderFactionName))
					fac.Allies[f.request.SenderFactionName] = time.Now()
					senderFac.Allies[fac.Name] = time.Now()
					utils.Panic(database.DB.SaveFaction(senderFac))
				}
				ut.Player().Message(text.Colourf(language.Translate(ut.Player()).FactionManage.Request.Accepted, server.Config.Prefix))
			} else {
				ut.Player().Message(text.Colourf(language.Translate(ut.Player()).FactionManage.Request.Rejected, server.Config.Prefix))
			}
		}
		fac.Requests = utils.Filter(fac.Requests, func(r1 *database.Request) bool {
			return r1 != f.request
		})
		utils.Panic(database.DB.SaveFaction(fac))
		ut.Data.Faction.Request = nil
	} else {
		FactionRequestsUI{}.SendTo(submitter.(*player.Player))
	}
}

func (f FactionRequestUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionRequestsUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionRequestUI) SendTo(pl *player.Player, r *database.Request) {
	fm := form.NewMenu(NewFactionRequestUI(r), text.Colourf("<aqua>%v</aqua>", r.LongType()))
	fm = fm.WithButtons(
		ui.AddButtonWithValue(pl, text.Colourf("<aqua>Accept</aqua>"), "", "accept"),
		ui.AddButtonWithValue(pl, text.Colourf("<aqua>Reject</aqua>"), "", "reject"),
		ui.AddButtonWithValue(pl, text.Colourf("<aqua>Back</aqua>"), "", "back"),
	)
	fm = fm.WithBody(text.Colourf(
		"<green>Sent By: <yellow>%v</yellow>\n"+
			"Sent At: <yellow>%v</yellow>\n"+
			"Message: <yellow>%v</yellow></green>",
		user.FactionNameDisplay.Name(user.GetUserByUUID(r.SentBy).Data),
		r.SentAt.String(),
		r.Description,
	))
	pl.SendForm(fm)
}

type FactionClaimUI struct {
	ObtainShovel form.Button
	Back         form.Button
}

func (f FactionClaimUI) Submit(submitter form.Submitter, button form.Button, _ *world.Tx) {
	pl := submitter.(*player.Player)
	switch button {
	case f.ObtainShovel:
		if _, err := pl.Inventory().AddItem(items.ClaimShovel{}.Stack()); err != nil {
			pl.Drop(items.ClaimShovel{}.Stack())
			pl.Message(text.Colourf(language.Translate(pl).Error.InventoryFull))
		}

		pl.Message(text.Colourf(language.Translate(pl).GiveShovel, server.Config.Prefix))
	case f.Back:
		FactionManageUI{}.SendTo(submitter.(*player.Player))
	}
}

func (f FactionClaimUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionManageUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionClaimUI) SendTo(pl *player.Player) {
	fm := form.NewMenu(FactionClaimUI{
		ObtainShovel: ui.AddButtonWithValue(pl, text.Colourf("<aqua>Obtain Claim Shovel</aqua>"), "", ""),
		Back:         ui.AddButtonWithValue(pl, text.Colourf("<aqua>Back</aqua>"), "", ""),
	}, text.Colourf("<aqua>Claim Info</aqua>"))
	fac := user.GetUser(pl).Data.Faction.Faction()

	fm = fm.WithBody(text.Colourf(
		"<green>Maximum Chunks Allowed: <yellow>%v</yellow>\n"+
			"<green>Remaining chunks to claim: <yellow>%v</yellow> chunks",
		fac.MaxChunksAllowed(),
		fac.RemainingChunks(),
	))
	pl.SendForm(fm)
}

// getFactionRoleName returns the faction role name without color formatting
func getFactionRoleName(role database.Role) string {
	switch role {
	case database.Leader:
		return "LEADER"
	case database.CoLeader:
		return "CO-LEADER"
	case database.Member:
		return "MEMBER"
	default:
		return "MEMBER"
	}
}
