package command

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/database"
	"server/server/language"
	"server/server/user"
	"server/server/utils"
)

type RankCommand struct {
	Player string       `cmd:"player"`
	Rank   ArgumentRank `cmd:"role"`
}

func (RankCommand) Allow(src cmd.Source) bool {
	return Rank.Test(src)
}

func (RankCommand) PermissionMessage(src cmd.Source) string {
	return Rank.PermissionMessage(src)
}

func (r RankCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		dt, err := database.DB.FindPlayerByName(r.Player)
		if err != nil {
			pl.Message(text.Colourf(language.Translate(pl).Error.PlayerNotExist))
			return
		}

		rank := database.RankFromName(string(r.Rank))

		dt.RankId = rank.Shortened()

		user.UpdateUserData(dt)
		utils.Panic(database.DB.SavePlayer(dt))

		if h, ok := server.MCServer.Player(dt.UUID); ok {
			go h.ExecWorld(func(tx *world.Tx, e world.Entity) {
				ut := user.GetUser(e.(*player.Player))
				e.(*player.Player).SetNameTag(user.FactionNameDisplay.Name(ut.Data))
			})
		}

		pl.Message(text.Colourf(language.Translate(pl).Commands.GiveRank.Success, server.Config.Prefix, dt.Username, rank.Prefix()))
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type ArgumentRank string

func (ArgumentRank) Type() string {
	return "rank"
}

func (ArgumentRank) Options(_ cmd.Source) []string {
	return database.ShortenedRanks
}
