package command

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server/database"
	"server/server/language"
	"server/server/user"
)

type Permission int

const (
	Rank Permission = iota
	WorldEdit
	Stop
	Fly
	RepairAll
	BeginnerKit
	VIPKit
	MVPKit
	MLPKit
	MMPKit
	MGPKit
	GameMode
	TP
)

func (p Permission) Test(src cmd.Source) bool {
	if pl, ok := src.(*player.Player); ok {
		if u := user.GetUser(pl); u != nil {
			pRank := u.Data.Rank()
			for pRank <= database.Player {
				for _, perm := range rankPermissions[pRank] {
					if perm == p {
						return true
					}
				}
				pRank++
			}
		}
		return false
	}
	return true
}

func (p Permission) PermissionMessage(src cmd.Source) string {
	if pl, ok := src.(*player.Player); ok && !p.Test(src) {
		for r, perms := range rankPermissions {
			for _, perm := range perms {
				if perm == p {
					return text.Colourf(language.Translate(pl).Commands.Permission, r.Prefix())
				}
			}
		}
	}
	return text.Colourf("<red>Something went wrong...</red>")
}

var rankPermissions = map[database.Rank][]Permission{
	database.Player:    {BeginnerKit},
	database.VIP:       {VIPKit, Fly, RepairAll},
	database.MVP:       {MVPKit},
	database.MMP:       {MMPKit},
	database.MLP:       {MLPKit},
	database.MGP:       {MGPKit},
	database.Youtuber:  {},
	database.Trainee:   {},
	database.Builder:   {},
	database.Support:   {},
	database.Moderator: {},
	database.Secretary: {GameMode, TP, Rank, WorldEdit},
	database.Admin:     {Stop},
	database.Owner:     {},
}
