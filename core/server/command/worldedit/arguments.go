package worldedit

import "github.com/df-mc/dragonfly/server/cmd"

type Block string

func (Block) Type() string {
	return "block"
}

func (Block) Options(_ cmd.Source) []string {
	keys := make([]string, 0, len(Blocks))
	for k := range Blocks {
		keys = append(keys, k)
	}
	return keys
}

type Angle string

func (Angle) Type() string {
	return "angle"
}

func (Angle) Options(_ cmd.Source) []string {
	return []string{"90", "180", "270"}
}
