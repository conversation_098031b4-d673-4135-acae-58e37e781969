package wecmd

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/command"
	"server/server/command/worldedit"
	"server/server/language"
	"server/server/user"
	"strconv"
)

type Rotate struct {
	Angle worldedit.Angle `cmd:"angle"`
}

func (Rotate) Allow(src cmd.Source) bool {
	return command.WorldEdit.Test(src)
}

func (Rotate) PermissionMessage(src cmd.Source) string {
	return command.WorldEdit.PermissionMessage(src)
}

func (t Rotate) Run(source cmd.Source, output *cmd.Output, tx *world.Tx) {
	var ok bool
	var pl *player.Player
	if pl, ok = source.(*player.Player); !ok {
		output.Error(text.Colourf("<redstone>You cannot use this command in console!</redstone>"))
		return
	}

	u := user.GetUser(pl)

	nullPos := cube.Pos{0, 0, 0}
	pos1 := u.WTData.Volume.Pos1
	pos2 := u.WTData.Volume.Pos2
	if pos1 == nullPos || pos2 == nullPos {
		output.Error(text.Colourf(language.Translate(pl).Commands.WorldTools.Error.PosNotSet))
		return
	}

	s, _ := strconv.ParseInt(string(t.Angle), 10, 64)
	angle := int(s)
	mpX := 1
	mpZ := 1
	swap := true
	rotates := 1
	switch angle {
	case 90:
		mpX *= -1
	case 180:
		mpX *= -1
		mpZ *= -1
		swap = false
		rotates = 2
	case 270:
		mpZ *= -1
		rotates = 3
	}
	selection := u.Selection()
	newSelection := map[cube.Pos]world.Block{}
	for pos, bl := range selection {
		dx := pos.X()
		dz := pos.Z()
		if swap {
			tmp := dx
			dx = dz
			dz = tmp
		}
		dx *= mpX
		dz *= mpZ

		stairs, ok := bl.(block.Stairs)
		if ok {
			for i := 0; i < rotates; i++ {
				stairs.Facing = stairs.Facing.RotateRight()
			}
			bl = stairs
		}
		newSelection[cube.Pos{dx, pos.Y(), dz}] = bl
	}

	oldSelection := map[cube.Pos]world.Block{}
	for pos, bl := range newSelection {
		if _, ok := bl.(block.Air); ok {
			continue
		}
		cpos := cube.PosFromVec3(pl.Position().Add(pos.Vec3()))
		oldSelection[cpos] = tx.Block(cpos)
		tx.SetBlock(cpos, bl, nil)
	}
	u.WTData.Undo.Push(oldSelection)
	output.Print(text.Colourf(language.Translate(pl).Commands.WorldTools.Fill, server.Config.WTPrefix))
}
