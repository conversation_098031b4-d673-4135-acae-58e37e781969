package wecmd

import (
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"golang.org/x/exp/rand"
	"math"
	"server/server"
	"server/server/command"
	"server/server/command/worldedit"
	"server/server/language"
	"server/server/user"
	"strconv"
	"strings"

	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/cmd"
)

type Fill struct {
	Blocks string `cmd:"block"`
}

func (Fill) Allow(src cmd.Source) bool {
	return command.WorldEdit.Test(src)
}

func (Fill) PermissionMessage(src cmd.Source) string {
	return command.WorldEdit.PermissionMessage(src)
}

func (t Fill) Run(source cmd.Source, output *cmd.Output, tx *world.Tx) {
	var ok bool
	var pl *player.Player
	if pl, ok = source.(*player.Player); !ok {
		output.Error(text.Colourf("<redstone>You cannot use this command in console!</redstone>"))
		return
	}

	u := user.GetUser(pl)

	nullPos := cube.Pos{0, 0, 0}
	pos1 := u.WTData.Volume.Pos1
	pos2 := u.WTData.Volume.Pos2
	if pos1 == nullPos || pos2 == nullPos {
		output.Error(language.Translate(pl).Commands.WorldTools.Error.PosNotSet)
		return
	}

	blocks := make(map[world.Block]int64) //block1%30, block2%70, etc..
	for _, blockArg := range strings.Split(t.Blocks, ",") {
		arg := strings.Split(blockArg, "%")
		var chance int64
		if len(arg) == 1 {
			chance = 100
		} else {
			chance, _ = strconv.ParseInt(arg[1], 10, 64)
		}
		b := worldedit.Blocks[arg[0]]
		if b == nil {
			output.Error(text.Colourf(language.Translate(pl).Commands.WorldTools.Error.BlockNotExist, arg[0]))
			return
		}
		blocks[b] = chance
	}
	oldSelection := map[cube.Pos]world.Block{}
	for x := int(math.Min(float64(pos1.X()), float64(pos2.X()))); x <= int(math.Max(float64(pos1.X()), float64(pos2.X()))); x++ {
		for y := int(math.Min(float64(pos1.Y()), float64(pos2.Y()))); y <= int(math.Max(float64(pos1.Y()), float64(pos2.Y()))); y++ {
			for z := int(math.Min(float64(pos1.Z()), float64(pos2.Z()))); z <= int(math.Max(float64(pos1.Z()), float64(pos2.Z()))); z++ {
				var b world.Block
				for block, chance := range blocks {
					if b == nil {
						b = block
					} else {
						if float64(rand.Intn(100)) < math.Abs(float64(chance)) {
							b = block
						}
					}
				}
				cPos := cube.Pos{x, y, z}
				oldSelection[cPos] = tx.Block(cPos)
				tx.SetBlock(cPos, b, nil)
			}
		}
	}
	u.WTData.Undo.Push(oldSelection)
	output.Print(text.Colourf(language.Translate(pl).Commands.WorldTools.Fill, server.Config.WTPrefix))
}
