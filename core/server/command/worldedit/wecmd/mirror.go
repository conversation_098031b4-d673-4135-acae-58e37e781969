package wecmd

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/command"
	"server/server/language"
	"server/server/user"
)

type Mirror struct{}

func (Mirror) Allow(src cmd.Source) bool {
	return command.WorldEdit.Test(src)
}

func (Mirror) PermissionMessage(src cmd.Source) string {
	return command.WorldEdit.PermissionMessage(src)
}

func (t Mirror) Run(source cmd.Source, output *cmd.Output, tx *world.Tx) {
	var ok bool
	var pl *player.Player
	if pl, ok = source.(*player.Player); !ok {
		output.Error(text.Colourf("<redstone>You cannot use this command in console!</redstone>"))
		return
	}

	u := user.GetUser(pl)

	nullPos := cube.Pos{0, 0, 0}
	pos1 := u.WTData.Volume.Pos1
	pos2 := u.WTData.Volume.Pos2
	if pos1 == nullPos || pos2 == nullPos {
		output.Error(text.Colourf(language.Translate(pl).Commands.WorldTools.Error.PosNotSet))
		return
	}

	plPos := pl.Position()

	selection := u.Selection()
	newSelection := map[cube.Pos]world.Block{}
	for pos, bl := range selection {
		if _, ok := bl.(block.Air); ok {
			continue
		}

		var newV mgl64.Vec3
		if pl.Rotation().Direction().Face() == cube.FaceNorth || pl.Rotation().Direction().Face() == cube.FaceSouth {
			newV = mgl64.Vec3{float64(pos.X()), float64(pos.Y()), plPos.Z() - (float64(pos.Z()) - plPos.Z())}
		} else {
			newV = mgl64.Vec3{plPos.X() - (float64(pos.X()) - plPos.X()), float64(pos.Y()), float64(pos.Z())}
		}
		tx.SetBlock(cube.PosFromVec3(newV), MirrorStair(bl, pl), nil)
		newSelection[cube.Pos{int(newV.X()), pos.Y(), int(newV.Z())}] = bl
	}

	oldSelection := map[cube.Pos]world.Block{}
	for pos, bl := range newSelection {
		if _, ok := bl.(block.Air); ok {
			continue
		}
		cpos := cube.PosFromVec3(pl.Position().Add(pos.Vec3()))
		oldSelection[cpos] = tx.Block(cpos)
		tx.SetBlock(cpos, bl, nil)
	}
	u.WTData.Undo.Push(oldSelection)
	output.Print(text.Colourf(language.Translate(pl).Commands.WorldTools.Mirror, server.Config.WTPrefix))
}

func MirrorStair(bl world.Block, p *player.Player) world.Block {
	if stair, ok := bl.(block.Stairs); ok {
		newID := stair.Facing
		horizontalFacing := p.Rotation().Direction().Face()

		if horizontalFacing == 3 || horizontalFacing == 2 {
			switch newID {
			case 2:
				newID = 3
			case 6:
				newID = 3
			case 3:
				newID = 2
			case 7:
				newID = 2
			}
		} else {
			switch newID {
			case 0:
				newID = 1
			case 4:
				newID = 1
			case 1:
				newID = 0
			case 5:
				newID = 0
			}
		}

		stair.Facing = newID
		return stair
	}

	return bl
}
