package wecmd

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/blocks"
	"server/server/command"
	"server/server/factions/enchants"
	"server/server/language"
	"server/server/user"
)

type Wand struct{}

func (Wand) Allow(src cmd.Source) bool {
	return command.WorldEdit.Test(src)
}

func (Wand) PermissionMessage(src cmd.Source) string {
	return command.WorldEdit.PermissionMessage(src)
}

func (Wand) Run(source cmd.Source, output *cmd.Output, _ *world.Tx) {
	var ok bool
	var pl *player.Player
	if pl, ok = source.(*player.Player); !ok {
		output.Error(text.Colourf("<redstone>You cannot use this command in console!</redstone>"))
		return
	}

	_, offHand := pl.HeldItems()
	pl.SetHeldItems(WandTool{}.Stack(), offHand)
	output.Printf(text.Colourf(language.Translate(pl).Commands.WorldTools.Wand, server.Config.WTPrefix))
}

func init() {
	blocks.RegisterSpecialItem(blocks.Wand, WandTool{})
}

type WandTool struct {
	item.Axe
}

func (w WandTool) Stack() item.Stack {
	s := item.NewStack(item.Axe{Tier: item.ToolTierWood}, 1).WithValue("special_item", int16(blocks.Wand)).WithEnchantments(item.NewEnchantment(enchants.Glitter{}, 1))
	s = s.WithCustomName(text.Colourf("<emerald>WT Wand</emerald>")).WithLore("Left click to set first position", "Right click to set second position")
	return s
}

func (w WandTool) UseOnBlock(pos cube.Pos, face cube.Face, _ mgl64.Vec3, tx *world.Tx, usr item.User, ctx *item.UseContext) (used bool) {
	pl := usr.(*player.Player)
	u := user.GetUser(pl)
	u.WTData.Volume.Pos1 = pos
	pl.Message(text.Colourf(language.Translate(pl).Commands.WorldTools.PosSet, server.Config.WTPrefix, 1, u.WTData.Volume.Pos1))
	return true
}

func (w WandTool) OnStartBreak(pl *player.Player, pos cube.Pos) bool {
	u := user.GetUser(pl)
	u.WTData.Volume.Pos2 = pos
	pl.Message(text.Colourf(language.Translate(pl).Commands.WorldTools.PosSet, server.Config.WTPrefix, 2, u.WTData.Volume.Pos2))
	return true
}
