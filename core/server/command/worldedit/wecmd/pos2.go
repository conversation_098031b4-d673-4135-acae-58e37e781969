package wecmd

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/command"
	"server/server/language"
	"server/server/user"
)

type Pos2 struct{}

func (Pos2) Allow(src cmd.Source) bool {
	return command.WorldEdit.Test(src)
}

func (Pos2) PermissionMessage(src cmd.Source) string {
	return command.WorldEdit.PermissionMessage(src)
}

func (t Pos2) Run(source cmd.Source, output *cmd.Output, _ *world.Tx) {
	var ok bool
	var pl *player.Player
	if pl, ok = source.(*player.Player); !ok {
		output.Error(text.Colourf("<redstone>You cannot use this command in console!</redstone>"))
		return
	}

	u := user.GetUser(pl)
	u.WTData.Volume.Pos2 = cube.PosFromVec3(pl.Position())
	output.Printf(text.Colourf(language.Translate(pl).Commands.WorldTools.PosSet, server.Config.WTPrefix, 2, u.WTData.Volume.Pos2))
}
