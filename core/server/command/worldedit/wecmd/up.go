package wecmd

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/command"
	"server/server/language"
)

type Up struct{}

func (Up) Allow(src cmd.Source) bool {
	return command.WorldEdit.Test(src)
}

func (Up) PermissionMessage(src cmd.Source) string {
	return command.WorldEdit.PermissionMessage(src)
}

func (up Up) Run(source cmd.Source, output *cmd.Output, tx *world.Tx) {
	var ok bool
	var pl *player.Player
	if pl, ok = source.(*player.Player); !ok {
		output.Error(text.Colourf("<redstone>You cannot use this command in console!</redstone>"))
		return
	}

	tx.SetBlock(cube.PosFromVec3(pl.Position().Add(mgl64.Vec3{0, -1, 0})), block.StainedGlass{Colour: item.ColourWhite()}, nil)

	output.Printf(text.Colourf(language.Translate(pl).Commands.WorldTools.Up, server.Config.WTPrefix))
}
