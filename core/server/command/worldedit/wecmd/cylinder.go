package wecmd

import (
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"golang.org/x/exp/rand"
	"math"
	"server/server"
	"server/server/command"
	"server/server/command/worldedit"
	"server/server/language"
	"server/server/user"
	"slices"
	"strconv"
	"strings"

	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/cmd"
)

type Cylinder struct {
	Radius int    `cmd:"radius"`
	Height int    `cmd:"height"`
	Blocks string `cmd:"block"`
}

func (Cylinder) Allow(src cmd.Source) bool {
	return command.WorldEdit.Test(src)
}

func (Cylinder) PermissionMessage(src cmd.Source) string {
	return command.WorldEdit.PermissionMessage(src)
}

func (c Cylinder) Run(source cmd.Source, output *cmd.Output, tx *world.Tx) {
	var ok bool
	var pl *player.Player
	if pl, ok = source.(*player.Player); !ok {
		output.Error(text.Colourf("<redstone>You cannot use this command in console!</redstone>"))
		return
	}

	u := user.GetUser(pl)

	blocks := make(map[world.Block]int64) //block1%30, block2%70, etc..
	for _, blockArg := range strings.Split(c.Blocks, ",") {
		arg := strings.Split(blockArg, "%")
		var chance int64
		if len(arg) == 1 {
			chance = 100
		} else {
			chance, _ = strconv.ParseInt(arg[1], 10, 64)
		}
		b := worldedit.Blocks[arg[0]]
		if b == nil {
			output.Error(text.Colourf(language.Translate(pl).Commands.WorldTools.Error.BlockNotExist, arg[0]))
			return
		}
		blocks[b] = chance
	}
	var filterSlice []cube.Pos
	oldSelection := map[cube.Pos]world.Block{}
	for r := 0; r < c.Radius; r++ {
		for O := 0.0; O < 360.0; O++ {
			for h := 0; h < c.Height; h++ {
				var b world.Block
				for block, chance := range blocks {
					if b == nil {
						b = block
					} else {
						if float64(rand.Intn(100)) < math.Abs(float64(chance)) {
							b = block
						}
					}
				}
				cPos := cube.Pos{
					int(pl.Position().X()) + int(float64(r)*math.Sin(O)),
					int(pl.Position().Y()) + h,
					int(pl.Position().Z()) + int(float64(r)*math.Cos(O)),
				}
				if !slices.Contains[[]cube.Pos](filterSlice, cPos) {
					filterSlice = append(filterSlice, cPos)
					oldSelection[cPos] = tx.Block(cPos)
					tx.SetBlock(cPos, b, nil)
				}
			}
		}
	}
	u.WTData.Undo.Push(oldSelection)
	output.Print(text.Colourf(language.Translate(pl).Commands.WorldTools.Cylinder, server.Config.WTPrefix, c.Radius, c.Height))
}
