package wecmd

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/command"
	"server/server/language"
	"server/server/user"
)

type Paste struct{}

func (Paste) Allow(src cmd.Source) bool {
	return command.WorldEdit.Test(src)
}

func (Paste) PermissionMessage(src cmd.Source) string {
	return command.WorldEdit.PermissionMessage(src)
}

func (Paste) Run(source cmd.Source, output *cmd.Output, tx *world.Tx) {
	var ok bool
	var pl *player.Player
	if pl, ok = source.(*player.Player); !ok {
		output.Error(text.Colourf("<redstone>You cannot use this command in console!</redstone>"))
		return
	}

	u := user.GetUser(pl)

	nullPos := cube.Pos{0, 0, 0}
	pos1 := u.WTData.Volume.Pos1
	pos2 := u.WTData.Volume.Pos2
	if pos1 == nullPos || pos2 == nullPos {
		output.Error(text.Colourf(language.Translate(pl).Commands.WorldTools.Error.PosNotSet))
		return
	}

	oldSelection := map[cube.Pos]world.Block{}
	plPos := pl.Position()
	for pos, bl := range u.Selection() {
		if _, ok := bl.(*block.Air); ok {
			continue
		}
		cpos := cube.PosFromVec3(plPos.Add(pos.Vec3()))
		oldSelection[cpos] = tx.Block(cpos)
		tx.SetBlock(cpos, bl, nil)
	}
	u.WTData.Undo.Push(oldSelection)
	output.Print(text.Colourf(language.Translate(pl).Commands.WorldTools.Paste, server.Config.WTPrefix))
}
