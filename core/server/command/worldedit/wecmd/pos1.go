package wecmd

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/command"
	"server/server/language"
	"server/server/user"
)

type Pos1 struct{}

func (Pos1) Allow(src cmd.Source) bool {
	return command.WorldEdit.Test(src)
}

func (Pos1) PermissionMessage(src cmd.Source) string {
	return command.WorldEdit.PermissionMessage(src)
}

func (t Pos1) Run(source cmd.Source, output *cmd.Output, _ *world.Tx) {
	var ok bool
	var pl *player.Player
	if pl, ok = source.(*player.Player); !ok {
		output.Error(text.Colourf("<redstone>You cannot use this command in console!</redstone>"))
		return
	}

	u := user.GetUser(pl)
	u.WTData.Volume.Pos1 = cube.PosFromVec3(pl.Position())
	output.Printf(text.Colourf(language.Translate(pl).Commands.WorldTools.PosSet, server.Config.WTPrefix, 1, u.WTData.Volume.Pos1))
}
