package wecmd

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/command"
	"server/server/language"
	"server/server/user"
)

type Redo struct{}

func (Redo) Allow(src cmd.Source) bool {
	return command.WorldEdit.Test(src)
}

func (Redo) PermissionMessage(src cmd.Source) string {
	return command.WorldEdit.PermissionMessage(src)
}

func (re Redo) Run(source cmd.Source, output *cmd.Output, tx *world.Tx) {
	var ok bool
	var pl *player.Player
	if pl, ok = source.(*player.Player); !ok {
		output.Error(text.Colourf("<redstone>You cannot use this command in console!</redstone>"))
		return
	}

	u := user.GetUser(pl)
	if u.WTData.Redo.Len() == 0 {
		output.Print(text.Colourf(language.Translate(pl).Commands.WorldTools.Error.NothingToUndo))
		return
	}

	oldSelection := map[cube.Pos]world.Block{}
	for pos, bl := range u.WTData.Redo.Pop().(map[cube.Pos]world.Block) {
		oldSelection[pos] = tx.Block(pos)
		tx.SetBlock(pos, bl, nil)
	}
	u.WTData.Undo.Push(oldSelection)

	output.Printf(text.Colourf(language.Translate(pl).Commands.WorldTools.Redo, server.Config.WTPrefix))
}
