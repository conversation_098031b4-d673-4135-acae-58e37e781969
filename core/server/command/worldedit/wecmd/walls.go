package wecmd

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"golang.org/x/exp/rand"
	"math"
	"server/server"
	"server/server/command"
	"server/server/command/worldedit"
	"server/server/language"
	"server/server/user"
	"strconv"
	"strings"
)

type Walls struct {
	Blocks string `wecmd:"block"`
}

func (Walls) Allow(src cmd.Source) bool {
	return command.WorldEdit.Test(src)
}

func (Walls) PermissionMessage(src cmd.Source) string {
	return command.WorldEdit.PermissionMessage(src)
}

func (t Walls) Run(source cmd.Source, output *cmd.Output, tx *world.Tx) {
	var ok bool
	var pl *player.Player
	if pl, ok = source.(*player.Player); !ok {
		output.Error(text.Colourf("<redstone>You cannot use this command in console!</redstone>"))
		return
	}

	u := user.GetUser(pl)

	nullPos := cube.Pos{0, 0, 0}
	pos1 := u.WTData.Volume.Pos1
	pos2 := u.WTData.Volume.Pos2
	if pos1 == nullPos || pos2 == nullPos {
		output.Error(text.Colourf(language.Translate(pl).Commands.WorldTools.Error.PosNotSet))
		return
	}

	blocks := make(map[world.Block]int64) //block1%30, block2%70, etc..
	for _, blockArg := range strings.Split(t.Blocks, ",") {
		arg := strings.Split(blockArg, "%")
		var chance int64
		if len(arg) == 1 {
			chance = 100
		} else {
			chance, _ = strconv.ParseInt(arg[1], 10, 64)
		}
		b := worldedit.Blocks[arg[0]]
		if b == nil {
			output.Error(text.Colourf(language.Translate(pl).Commands.WorldTools.Error.BlockNotExist, server.Config.WTPrefix, arg[0]))
			return
		}
		blocks[b] = chance
	}

	box := cube.Box(float64(pos1.X()), float64(pos1.Y()), float64(pos1.Z()), float64(pos2.X()), float64(pos2.Y()), float64(pos2.Z()))

	oldSelection := map[cube.Pos]world.Block{}
	for x := box.Min().X(); x <= box.Max().X(); x++ {
		for y := box.Min().Y(); y <= box.Max().Y(); y++ {
			for z := box.Min().Z(); z <= box.Max().Z(); z++ {
				if x == box.Min().X() || x == box.Max().X() || z == box.Min().Z() || z == box.Max().Z() {
					var b world.Block
					for block, chance := range blocks {
						if b == nil {
							b = block
						} else {
							if float64(rand.Intn(100)) < math.Abs(float64(chance)) {
								b = block
							}
						}
					}
					cpos := cube.Pos{int(x), int(y), int(z)}
					oldSelection[cpos] = tx.Block(cpos)
					tx.SetBlock(cpos, b, nil)
				}
			}
		}
	}
	u.WTData.Undo.Push(oldSelection)
	output.Printf(text.Colourf(language.Translate(pl).Commands.WorldTools.Wall, server.Config.WTPrefix))
}
