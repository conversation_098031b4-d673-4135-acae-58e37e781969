package worldedit

import (
	"github.com/df-mc/dragonfly/server/world"
	"strconv"
	"strings"
	_ "unsafe"
)

var Blocks map[string]world.Block

//go:linkname dfBlocks github.com/df-mc/dragonfly/server/world.blocks
var dfBlocks []world.Block

func RefreshBlocks() bool {
	Blocks = map[string]world.Block{}
	for _, v := range dfBlocks {
		id, _ := v.EncodeBlock()
		if _, ok := Blocks[strings.Replace(id, "minecraft:", "", 1)]; ok {
			foundCount := 0
			for k := range Blocks {
				if k == strings.Replace(id, "minecraft:", "", 1) {
					foundCount++
				}
			}
			id = id + "_" + strconv.Itoa(foundCount)
		}
		Blocks[strings.Replace(id, "minecraft:", "", 1)] = v
	}
	return true
}
