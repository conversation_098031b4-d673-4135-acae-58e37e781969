package projectiles

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/block/model"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"math"
	"server/server/utils"
	"time"
)

type Ray struct {
	Start    mgl64.Vec3
	End      mgl64.Vec3
	Cooldown time.Duration
	Damage   float64
}

func (r Ray) AddParticles(particle world.Particle, owner *living.Living) {
	direction := r.End.Sub(r.Start)

	length := math.Sqrt(direction.X()*direction.X() + direction.Y()*direction.Y() + direction.Z()*direction.Z())

	direction = mgl64.Vec3{
		direction.X() / length,
		direction.Y() / length,
		direction.Z() / length,
	}

	go func() {
		for d := 0.0; d <= length+15; d += 2 {
			ppos := r.Start.Add(direction.Mul(d))
			owner.H().ExecWorld(func(tx *world.Tx, _ world.Entity) {
				tx.AddParticle(ppos, particle)
				for e := range tx.Players() {
					pl, _ := e.(*player.Player)
					if utils.Distance(pl.Position(), ppos) < 1 {
						pl.Hurt(r.Damage, entity.AttackDamageSource{Attacker: owner})
					}
				}
				b := tx.Block(cube.PosFromVec3(ppos))
				if _, ok := b.Model().(model.Empty); !ok && utils.RandChance(75) {
					tx.SetBlock(cube.PosFromVec3(ppos), block.Air{}, nil)
				}
			})
			time.Sleep(r.Cooldown)
		}
	}()
}

func (r Ray) AddBlocks(block world.Block, owner *living.Living, tx *world.Tx) {
	direction := r.End.Sub(r.Start)

	length := math.Sqrt(direction.X()*direction.X() + direction.Y()*direction.Y() + direction.Z()*direction.Z())

	direction = mgl64.Vec3{
		direction.X() / length,
		direction.Y() / length,
		direction.Z() / length,
	}

	for d := 0.0; d <= length+40; d++ {
		ppos := r.Start.Add(direction.Mul(d))
		tx.SetBlock(cube.PosFromVec3(ppos), block, nil)
		for e := range tx.Players() {
			pl, _ := e.(*player.Player)
			if utils.Distance(pl.Position(), ppos) < 1 {
				pl.Hurt(r.Damage, entity.AttackDamageSource{Attacker: owner})
			}
		}
	}
}
