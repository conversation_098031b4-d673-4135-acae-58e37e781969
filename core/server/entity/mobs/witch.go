package mobs

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/item/potion"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"math/rand"
	"server/server/blocks/heads/witch"
	"server/server/utils"
	"time"
)

type Witch struct {
	living.NopLivingType

	headDropper
	shooter
}

var potions = []potion.Potion{potion.Harming(), potion.LongPoison(), potion.LongSlowness(), potion.LongWeakness(), potion.Wither(), potion.StrongSlowness(), potion.StrongPoison(), potion.StrongHarming()}

func NewWitch(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	m := Witch{}
	timeNow := time.Now()
	conf := living.Config{
		EntityType: m,
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.7,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Speed:          0.1,
		EyeHeight:      0.5,
		MaxHealth:      26,
		Drops:          []living.Drop{living.NewDrop(item.SplashPotion{Type: potions[rand.Intn(len(potions))]}, 1, 2)},
		ImmuneDuration: 350 * time.Millisecond,
		Handler: &MobHandler{
			mob:       m,
			targetPos: &pos,
			shotAt:    &timeNow,
			otherBehavior: func(l *living.Living, targetPos *mgl64.Vec3, _ *time.Time, tx *world.Tx) {
				nearestPl, minD := utils.NearestPlayer(l, tx)
				if nearestPl != nil && minD < 10 {
					main, off := nearestPl.HeldItems()
					_, ok1 := main.Item().(block.WheatSeeds)
					_, ok2 := off.Item().(block.WheatSeeds)
					if ok1 || ok2 {
						l.MoveToTarget(nearestPl.Position(), 0.1, tx)
						l.LookAt(nearestPl.Position(), tx)
						*targetPos = l.Position()
					}
				}
			},
		},
	}
	l := tx.AddEntity(world.EntitySpawnOpts{NameTag: "<green>Witch</green>", Position: pos}.New(conf.EntityType, conf)).(*living.Living)
	RefreshName(l, l.Health())
	return l
}

func (Witch) EncodeEntity() string {
	return "minecraft:witch"
}
func (Witch) BBox(world.Entity) cube.BBox {
	return cube.Box(-0.3, 0, -0.3, 0.3, 1.9, 0.3)
}

func (Witch) Type() MobHead {
	return MobHead{Block: witch.Head{}}
}

func (Witch) ShootingRange() float64 {
	return 5
}

func (Witch) ShootingSpeed() time.Duration {
	return 2 * time.Second
}

func (Witch) Projectile(l *living.Living) *world.EntityHandle {
	return entity.NewSplashPotion(world.EntitySpawnOpts{Position: l.Position().Add(mgl64.Vec3{0, l.EyeHeight() + 1, 0})}, potions[rand.Intn(len(potions))], l)
}

func (Witch) ProjectileSpeedMultiplier() float64 {
	return 1.25
}
