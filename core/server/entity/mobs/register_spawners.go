package mobs

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/world"
	"server/server/factions/spawner"
)

func init() {
	spawner.RegisterEntityType(Chicken{}, func(pos cube.Pos, tx *world.Tx) *world.EntityHandle {
		return NewChicken(pos.Vec3(), tx).H()
	})

	spawner.RegisterEntityType(<PERSON><PERSON>{}, func(pos cube.Pos, tx *world.Tx) *world.EntityHandle {
		return NewEnderman(pos.Vec3(), tx).H()
	})

	spawner.RegisterEntityType(IronGolem{}, func(pos cube.Pos, tx *world.Tx) *world.EntityHandle {
		return NewIronGolem(pos.Vec3(), tx).H()
	})

	spawner.RegisterEntityType(Pillager{}, func(pos cube.Pos, tx *world.Tx) *world.EntityHandle {
		return NewPillager(pos.Vec3(), tx).H()
	})

	spawner.RegisterEntityType(PolarBear{}, func(pos cube.Pos, tx *world.Tx) *world.EntityHandle {
		return NewPolarBear(pos.Vec3(), tx).H()
	})

	spawner.RegisterEntityType(Skeleton{}, func(pos cube.Pos, tx *world.Tx) *world.EntityHandle {
		return NewSkeleton(pos.Vec3(), tx).H()
	})

	spawner.RegisterEntityType(Slime{}, func(pos cube.Pos, tx *world.Tx) *world.EntityHandle {
		return NewWitherSkeleton(pos.Vec3(), tx).H()
	})

	spawner.RegisterEntityType(SnowGolem{}, func(pos cube.Pos, tx *world.Tx) *world.EntityHandle {
		return NewSnowGolem(pos.Vec3(), tx).H()
	})

	spawner.RegisterEntityType(Witch{}, func(pos cube.Pos, tx *world.Tx) *world.EntityHandle {
		return NewWitch(pos.Vec3(), tx).H()
	})

	spawner.RegisterEntityType(WitherSkeleton{}, func(pos cube.Pos, tx *world.Tx) *world.EntityHandle {
		return NewWitherSkeleton(pos.Vec3(), tx).H()
	})

	spawner.RegisterEntityType(Zombie{}, func(pos cube.Pos, tx *world.Tx) *world.EntityHandle {
		return NewZombie(pos.Vec3(), tx).H()
	})
}
