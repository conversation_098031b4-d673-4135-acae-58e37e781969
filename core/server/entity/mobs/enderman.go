package mobs

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"math/rand"
	"server/server/blocks/heads/enderman"
	"time"
)

type Enderman struct {
	living.NopLivingType

	headDropper
	attacker
}

func NewEnderman(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	m := Enderman{}
	timeNow := time.Now()
	conf := living.Config{
		EntityType: m,
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.7,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Speed:          0.2,
		EyeHeight:      1.5,
		MaxHealth:      40,
		Drops:          []living.Drop{living.NewDrop(item.EnderPearl{}, 2, 3), living.NewDrop(block.EndStone{}, 1, 2)},
		ImmuneDuration: 350 * time.Millisecond,
		Handler: &MobHandler{
			mob:       m,
			targetPos: &pos,
			shotAt:    &timeNow,
			otherBehavior: func(l *living.Living, targetPos *mgl64.Vec3, _ *time.Time, tx *world.Tx) {
				insideBlocks := []world.Block{
					tx.Block(cube.PosFromVec3(l.Position())),
					tx.Block(cube.PosFromVec3(l.Position().Add(mgl64.Vec3{0, 1, 0}))),
					tx.Block(cube.PosFromVec3(l.Position().Add(mgl64.Vec3{0, 2, 0}))),
				}

				for _, bl := range insideBlocks {
					if _, ok := bl.(block.Water); ok {
						l.Hurt(1, entity.SuffocationDamageSource{})

						// TODO: teleporter interface is not exported for whatever reason so this is a hacky solution to teleport the ender man
						oldS := l.Speed()
						l.SetSpeed(100)
						x := l.Position().X() + float64(rand.Intn(10))
						z := l.Position().Z() + float64(rand.Intn(10))
						y := float64(tx.HighestBlock(int(x), int(z)))
						l.MoveToTarget(mgl64.Vec3{x, y + 4, z}, 0.5, tx)
						l.SetSpeed(oldS)
						break
					}
				}
			},
		},
	}
	l := tx.AddEntity(world.EntitySpawnOpts{NameTag: "<green>Enderman</green>", Position: pos}.New(conf.EntityType, conf)).(*living.Living)
	RefreshName(l, l.Health())
	return l
}

func (Enderman) EncodeEntity() string {
	return "minecraft:enderman"
}
func (Enderman) BBox(world.Entity) cube.BBox {
	return cube.Box(-0.3, 0, -0.3, 0.3, 2.9, 0.3)
}

func (Enderman) Type() MobHead {
	return MobHead{Block: enderman.Head{}}
}

func (Enderman) AttackRange() float64 {
	return 1.5
}
func (Enderman) Damage(*player.Player) float64 {
	return 2
}
