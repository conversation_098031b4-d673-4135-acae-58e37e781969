package mobs

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/entity/effect"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"time"
)

type WitherSkeleton struct {
	living.NopLivingType

	headDropper
	attacker
}

func NewWitherSkeleton(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	m := WitherSkeleton{}
	timeNow := time.Now()
	conf := living.Config{
		EntityType: m,
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.7,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Speed:          0.2,
		EyeHeight:      1,
		MaxHealth:      20,
		Drops:          []living.Drop{living.NewDrop(item.Bone{}, 4, 5)},
		ImmuneDuration: 350 * time.Millisecond,
		Handler: &MobHandler{
			mob:       m,
			targetPos: &pos,
			shotAt:    &timeNow,
		},
	}
	l := tx.AddEntity(world.EntitySpawnOpts{NameTag: "<green>Wither Skeleton</green>", Position: pos}.New(conf.EntityType, conf)).(*living.Living)
	RefreshName(l, l.Health())
	return l
}

func (WitherSkeleton) EncodeEntity() string {
	return "minecraft:wither_skeleton"
}
func (WitherSkeleton) BBox(world.Entity) cube.BBox {
	return cube.Box(-0.43, 0, -0.43, 0.43, 2.4, 0.43)
}

func (WitherSkeleton) Type() MobHead {
	return MobHead{SkullType: block.WitherSkeletonSkull()}
}

func (WitherSkeleton) AttackRange() float64 {
	return 1.5
}
func (WitherSkeleton) Damage(pl *player.Player) float64 {
	pl.AddEffect(effect.New(effect.Wither, 1, 5*time.Second))
	return 4
}
