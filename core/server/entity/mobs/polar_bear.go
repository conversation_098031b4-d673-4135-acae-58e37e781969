package mobs

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"server/server/blocks/heads/polar_bear"
	"time"
)

type PolarBear struct {
	living.NopLivingType

	headDropper
	attacker
}

func NewPolarBear(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	m := PolarBear{}
	timeNow := time.Now()
	conf := living.Config{
		EntityType: m,
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.7,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Speed:          0.1,
		EyeHeight:      1,
		MaxHealth:      30,
		Drops:          []living.Drop{living.NewDrop(item.Leather{}, 5, 6), living.NewDrop(item.Bone{}, 3, 4)},
		ImmuneDuration: 350 * time.Millisecond,
		Handler: &MobHandler{
			mob:       m,
			targetPos: &pos,
			shotAt:    &timeNow,
		},
	}
	l := tx.AddEntity(world.EntitySpawnOpts{NameTag: "<green>Polar Bear</green>", Position: pos}.New(conf.EntityType, conf)).(*living.Living)
	RefreshName(l, l.Health())
	return l
}

func (PolarBear) EncodeEntity() string {
	return "minecraft:polar_bear"
}
func (PolarBear) BBox(world.Entity) cube.BBox {
	return cube.Box(-0.65, 0, -0.65, 0.65, 1.4, 0.65)
}

func (PolarBear) Type() MobHead {
	return MobHead{Block: polar_bear.Head{}}
}

func (PolarBear) AttackRange() float64 {
	return 1.5
}

func (PolarBear) Damage(*player.Player) float64 {
	return 4.5
}
