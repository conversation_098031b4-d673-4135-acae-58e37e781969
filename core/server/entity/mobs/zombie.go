package mobs

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"time"
)

type Zombie struct {
	living.NopLivingType

	headDropper
	attacker
}

func NewZombie(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	m := Zombie{}
	timeNow := time.Now()
	conf := living.Config{
		EntityType: m,
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.7,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Speed:          0.1,
		EyeHeight:      0.5,
		MaxHealth:      20,
		Drops:          []living.Drop{living.NewDrop(item.IronIngot{}, 0, 1), living.NewDrop(block.Carrot{}, 0, 1), living.NewDrop(block.Potato{}, 0, 1)},
		ImmuneDuration: 350 * time.Millisecond,
		Handler: &MobHandler{
			mob:       m,
			targetPos: &pos,
			shotAt:    &timeNow,
		},
	}
	l := tx.AddEntity(world.EntitySpawnOpts{NameTag: "<green>Zombie</green>", Position: pos}.New(conf.EntityType, conf)).(*living.Living)
	RefreshName(l, l.Health())
	return l
}

func (Zombie) EncodeEntity() string {
	return "minecraft:zombie"
}
func (Zombie) BBox(world.Entity) cube.BBox {
	return cube.Box(-0.3, 0, -0.3, 0.3, 1.9, 0.3)
}

func (Zombie) Type() MobHead {
	return MobHead{SkullType: block.ZombieHead()}
}

func (Zombie) AttackRange() float64 {
	return 1.5
}

func (Zombie) Damage(*player.Player) float64 {
	return 2.5
}
