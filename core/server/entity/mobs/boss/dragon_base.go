package boss

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/bossbar"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/dragonfly/server/world/particle"
	"github.com/go-gl/mathgl/mgl64"
	"math"
	"math/rand"
	"server/server/entity/projectiles"
	"server/server/utils"
	"time"
)

func playAnimation(l *living.Living, tx *world.Tx, animation string) {
	for e := range tx.Players() {
		utils.Session(e.(*player.Player)).ViewEntityAnimation(l, world.NewEntityAnimation(animation))
	}
}

type Dragon struct {
	living.NopLivingType
	Tier              int
	DragonIdentifier  string
	BreathingParticle world.Particle
	BossBar           bossbar.BossBar

	tick     time.Duration
	prevAnim string
	animCd   time.Duration
	attackCD time.Duration
	a        float64
}

func (d Dragon) EncodeEntity() string {
	return d.DragonIdentifier
}
func (Dragon) BBox(world.Entity) cube.BBox {
	return cube.Box(-2.28, 0, -5.75, 2.28, 4.56, 5.75)
}

type DragonHandler struct {
	living.NopHandler
	D             *Dragon
	otherBehavior func(*living.Living, *world.Tx)
}

func (h DragonHandler) HandleTick(ctx living.Context, tx *world.Tx) {
	l := ctx.Val()

	// Animation handler
	if h.D.tick%(100*time.Millisecond) == 0 {
		if h.D.animCd == 0 {
			pos := l.Position()
			y := tx.HighestBlock(int(pos.X()), int(pos.Z()))

			if int(l.Position().Y()) > y+2 {
				playAnimation(l, tx, "flying")
				h.D.prevAnim = "flying"
				h.D.animCd = 1900 * time.Millisecond
			} else {
				if h.D.prevAnim == "flying" {
					playAnimation(l, tx, "transition-fly")
					h.D.animCd = 900 * time.Millisecond
				} else {
					playAnimation(l, tx, "walking")
					h.D.animCd = 1400 * time.Millisecond
				}
				h.D.prevAnim = "walking"
			}
		}
		if h.D.animCd > 0 {
			h.D.animCd -= 100 * time.Millisecond
		}
	}

	if h.otherBehavior != nil {
		ha := l.H()
		go func() {
			ha.ExecWorld(func(tx *world.Tx, e world.Entity) {
				h.otherBehavior(l, tx)
			})
		}()
	}

	if h.D.tick >= 10*time.Minute {
		err := l.Close()
		if err != nil {
			panic(err)
		}
		return
	}

	for e := range tx.Players() {
		pl, _ := e.(*player.Player)
		d := utils.Distance(l.Position(), pl.Position())
		if pl != nil && d <= 128 && utils.HasLineOfSight(tx, l.Position(), pl.Position()) {
			x := pl.Position().X() + 20*math.Sin(h.D.a)
			z := pl.Position().Z() + 20*math.Cos(h.D.a)
			y := float64(tx.HighestBlock(int(pl.Position().X()), int(pl.Position().Z()))) + 40
			h.D.a += 0.025

			utils.DragonMoveToTarget(l, tx, mgl64.Vec3{x, y, z})

			if h.D.attackCD == 0 {
				var cd time.Duration
				if h.D.Tier <= 3 {
					cd = 50 * time.Millisecond
				} else {
					cd = 35 * time.Millisecond
				}

				for x := -1.0; x <= 1; x++ {
					for y := -1.0; y <= 1; y++ {
						for z := -1.0; z <= 1; z++ {
							rot := l.Rotation().Vec3()
							ray := projectiles.Ray{
								Start:    l.Position().Add(mgl64.Vec3{rot.X(), 0, rot.Z()}.Mul(12)),
								End:      pl.Position().Add(mgl64.Vec3{x, y, z}),
								Cooldown: cd,
								Damage:   1 + 0.25*float64(5-h.D.Tier),
							}
							ray.AddParticles(particle.Evaporate{}, l)
						}
					}
				}
				h.D.attackCD = 2 * time.Second
			}
		}
	}
	if h.D.attackCD > 0 {
		h.D.attackCD -= 50 * time.Millisecond
	}
	h.D.tick += 50 * time.Millisecond
}

func (h DragonHandler) HandleHurt(ctx living.Context, damage float64, _ bool, _ *time.Duration, src world.DamageSource) {
	l := ctx.Val()
	if _, ok := src.(entity.FallDamageSource); ok {
		ctx.Cancel()
		return
	}

	if src1, ok := src.(entity.AttackDamageSource); ok {
		if pl, ok := src1.Attacker.(*player.Player); ok {
			onHurt(l, h.D, damage, pl)
		}
	} else if src2, ok := src.(entity.ProjectileDamageSource); ok {
		if pl, ok := src2.Owner.(*player.Player); ok {
			onHurt(l, h.D, damage, pl)
		}
	}
}

func onHurt(l *living.Living, d *Dragon, damage float64, pl *player.Player) {
	if l.Health() < damage {
		tx := l.Tx()
		for _, orb := range entity.NewExperienceOrbs(l.Position(), 15*d.Tier) {
			if e, ok := orb.Entity(tx); ok {
				e.(*entity.Ent).SetVelocity(mgl64.Vec3{rand.Float64()*2 - 1, 1, rand.Float64()*2 - 1})
			}
			tx.AddEntity(orb)
		}
		for p := range tx.Players() {
			p.(*player.Player).RemoveBossBar()
		}
	} else {
		d.BossBar = d.BossBar.WithHealthPercentage(l.Health() / l.MaxHealth())
		pl.SendBossBar(d.BossBar)
	}
}
