package boss

import (
	"github.com/ThronesMC/camera"
	"github.com/ThronesMC/camera/instructions"
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/bossbar"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/dragonfly/server/world/particle"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"image/color"
	"math/rand"
	"server/server/database"
	"server/server/factions/items"
	"server/server/utils"
	"time"
)

func NewLightDragon(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	d := &Dragon{
		Tier:              1 + rand.Intn(5),
		DragonIdentifier:  "custom:light_dragon",
		BreathingParticle: particle.Dust{Colour: color.RGBA{R: 240, G: 251, B: 62}},
	}

	amount := []int{5 + rand.Intn(21)*1000, 50 + rand.Intn(41)*1000, 100 + rand.Intn(101)*1000, 300 + rand.Intn(101)*1000, 500 + rand.Intn(101)*1000}[d.Tier-1]
	chosenRank := database.Player
	for _, rnk := range []database.Rank{database.MGP, database.MLP, database.MMP, database.MVP, database.VIP} {
		if utils.RandChance(35) {
			chosenRank = rnk
			break
		}
	}

	conf := living.Config{
		EntityType: d,
		MaxHealth:  float64(200 * d.Tier),
		Speed:      1,
		Drops: []living.Drop{
			living.NewDropWithStack(items.BankNote{Amount: float64(amount)}.Stack()),
			living.NewDropWithStack(items.Kit{Type: chosenRank}.Stack()),
			living.NewDropWithStack(item.NewStack(item.BottleOfEnchanting{}, 300)),
		},
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.01,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Handler: &DragonHandler{
			D: d,
			otherBehavior: func(l *living.Living, tx *world.Tx) {
				if d.tick%(time.Second) == 0 {
					for e := range tx.Players() {
						pl, _ := e.(*player.Player)
						if utils.Distance(pl.Position(), l.Position()) < 64 {
							for d.tick%(500*time.Millisecond) == 0 {
								lightning := entity.NewLightningWithDamage(
									world.EntitySpawnOpts{Position: pl.Position().Add(mgl64.Vec3{float64(-10 + rand.Intn(20)), 0, float64(-10 + rand.Intn(20))})},
									2,
									true,
									250*time.Millisecond,
								)
								tx.AddEntity(lightning)
								if utils.RandChance(5) {
									camera.SendCameraInstruction(pl, instructions.FadeCameraInstruction{
										FadeInDuration:  500 * time.Millisecond,
										WaitDuration:    1 * time.Second,
										FadeOutDuration: 500 * time.Millisecond,
										Colour:          color.RGBA{R: 255, G: 255, A: 200},
									})
								}
								break
							}
						}
					}
				}
			},
		},
	}

	l := tx.AddEntity(world.EntitySpawnOpts{
		NameTag:  text.Colourf("<gold>[<dark-red>%v</dark-red>]</gold> <bold><dark-yellow>Light Dragon</dark-yellow></bold>", d.Tier),
		Position: pos,
	}.New(conf.EntityType, conf)).(*living.Living)
	d.BossBar = bossbar.New(l.NameTag()).WithColour(bossbar.Yellow()).WithHealthPercentage(1)
	return l
}
