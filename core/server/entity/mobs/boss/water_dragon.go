package boss

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player/bossbar"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/dragonfly/server/world/particle"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"math"
	"math/rand"
	"server/server/database"
	"server/server/factions/items"
	"server/server/utils"
	"slices"
	"time"
)

func NewWaterDragon(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	d := &Dragon{
		Tier:              1 + rand.Intn(5),
		DragonIdentifier:  "custom:water_dragon",
		BreathingParticle: particle.WaterDrip{},
	}

	amount := []int{5 + rand.Intn(21)*1000, 50 + rand.Intn(41)*1000, 100 + rand.Intn(101)*1000, 300 + rand.Intn(101)*1000, 500 + rand.Intn(101)*1000}[d.Tier-1]
	chosenRank := database.Player
	for _, rnk := range []database.Rank{database.MGP, database.MLP, database.MMP, database.MVP, database.VIP} {
		if utils.RandChance(35) {
			chosenRank = rnk
			break
		}
	}

	conf := living.Config{
		EntityType: d,
		MaxHealth:  float64(200 * d.Tier),
		Speed:      1,
		Drops: []living.Drop{
			living.NewDropWithStack(items.BankNote{Amount: float64(amount)}.Stack()),
			living.NewDropWithStack(items.Kit{Type: chosenRank}.Stack()),
			living.NewDropWithStack(item.NewStack(item.BottleOfEnchanting{}, 300)),
		},
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.01,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Handler: &DragonHandler{
			D: d,
			otherBehavior: func(l *living.Living, tx *world.Tx) {
				if d.tick%(5*time.Second) == 0 {
					pl, _ := utils.NearestPlayer(l, tx)
					if pl != nil {
						var filter []mgl64.Vec3
						go func() {
							l.H().ExecWorld(func(tx *world.Tx, _ world.Entity) {
								for r := 0; r < 3; r++ {
									for O := 0.0; O < 360.0; O++ {
										for a := -90.0; a < 90.0; a++ {
											newPos := l.Position().Add(mgl64.Vec3{
												math.Round(float64(r) * math.Sin(a) * math.Sin(O)),
												math.Round(float64(r)*math.Cos(a)) + 5,
												math.Round(float64(r) * math.Sin(a) * math.Cos(O)),
											})
											if !slices.Contains(filter, newPos) {
												filter = append(filter, newPos)
												tx.AddEntity(entity.NewFallingBlock(world.EntitySpawnOpts{Position: newPos, Velocity: pl.Position().Sub(l.Position()).Normalize().Mul(5)}, block.BlueIce{}))
											}
										}
									}
								}
							})
						}()
					}
				}
			},
		},
	}

	l := tx.AddEntity(world.EntitySpawnOpts{
		NameTag:  text.Colourf("<gold>[<dark-red>%v</dark-red>]</gold> <bold><blue>Water Dragon</blue></bold>", d.Tier),
		Position: pos,
	}.New(conf.EntityType, conf)).(*living.Living)

	d.BossBar = bossbar.New(l.NameTag()).WithColour(bossbar.Yellow()).WithHealthPercentage(1)
	return l
}
