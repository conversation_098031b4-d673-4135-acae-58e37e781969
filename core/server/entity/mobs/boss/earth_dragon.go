package boss

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/bossbar"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/dragonfly/server/world/particle"
	"github.com/df-mc/dragonfly/server/world/sound"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"image/color"
	"math"
	"math/rand"
	"server/server/database"
	"server/server/entity/mobs"
	"server/server/factions/items"
	"server/server/utils"
	"slices"
	"time"
)

func NewEarthDragon(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	d := &Dragon{
		Tier:              1 + rand.Intn(5),
		DragonIdentifier:  "custom:earth_dragon",
		BreathingParticle: particle.Dust{Colour: color.RGBA{R: 124, G: 252, B: 0}},
	}

	amount := []int{5 + rand.Intn(21)*1000, 50 + rand.Intn(41)*1000, 100 + rand.Intn(101)*1000, 300 + rand.Intn(101)*1000, 500 + rand.Intn(101)*1000}[d.Tier-1]
	chosenRank := database.Player
	for _, rnk := range []database.Rank{database.MGP, database.MLP, database.MMP, database.MVP, database.VIP} {
		if utils.RandChance(35) {
			chosenRank = rnk
			break
		}
	}

	conf := living.Config{
		EntityType: d,
		MaxHealth:  float64(200 * d.Tier),
		Speed:      1,
		Drops: []living.Drop{
			living.NewDropWithStack(items.BankNote{Amount: float64(amount)}.Stack()),
			living.NewDropWithStack(items.Kit{Type: chosenRank}.Stack()),
			living.NewDropWithStack(item.NewStack(item.BottleOfEnchanting{}, 300)),
		},
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.01,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Handler: &DragonHandler{
			D: d,
			otherBehavior: func(l *living.Living, tx *world.Tx) {
				if d.tick%(5*time.Second) == 0 {
					for e := range tx.Players() {
						pl, _ := e.(*player.Player)
						if utils.Distance(pl.Position(), l.Position()) < 64 {
							var blockEntities []*world.EntityHandle
							var filter []mgl64.Vec3
							for r := 0; r < 3; r++ {
								for O := 0.0; O < 360.0; O++ {
									for a := -90.0; a < 90.0; a++ {
										newPos := pl.Position().Add(mgl64.Vec3{
											math.Round(float64(r) * math.Sin(a) * math.Sin(O)),
											math.Round(float64(r)*math.Cos(a)) + 10,
											math.Round(float64(r) * math.Sin(a) * math.Cos(O)),
										})
										if !slices.Contains(filter, newPos) {
											filter = append(filter, newPos)
											blocks := []world.Block{block.Dirt{}, block.Dirt{Coarse: true}, block.Cobblestone{}, block.Cobblestone{Mossy: true}, block.Stone{}}
											blockEntities = append(blockEntities, entity.NewFallingBlock(world.EntitySpawnOpts{Position: newPos}, blocks[rand.Intn(len(blocks))]))
										}
									}
								}
							}
							for _, ent := range blockEntities {
								tx.AddEntity(ent)
							}
						}
					}
				}

				if d.Tier > 3 && d.tick%(35*time.Second) == 0 {
					for e := range tx.Players() {
						pl, _ := e.(*player.Player)
						if utils.Distance(pl.Position(), l.Position()) < 64 {
							for d.tick%(500*time.Millisecond) == 0 {
								newPos := pl.Position().Add(mgl64.Vec3{float64(2 + rand.Intn(8)), 4, float64(2 + rand.Intn(8))})
								mobs.NewIronGolem(newPos, tx)
								tx.AddParticle(newPos, particle.Dust{Colour: color.RGBA{R: 255, G: 255, B: 255}})
								for e1 := range tx.Players() {
									pl1, _ := e1.(*player.Player)
									if utils.Distance(pl1.Position(), newPos) < 20 {
										pl1.PlaySound(sound.FireExtinguish{})
									}
								}
								break
							}
						}
					}
				}
			},
		},
	}

	l := tx.AddEntity(world.EntitySpawnOpts{
		NameTag:  text.Colourf("<gold>[<dark-red>%v</dark-red>]</gold> <bold><green>Earth Dragon</green></bold>", d.Tier),
		Position: pos,
	}.New(conf.EntityType, conf)).(*living.Living)
	d.BossBar = bossbar.New(l.NameTag()).WithColour(bossbar.Green()).WithHealthPercentage(1)
	return l
}
