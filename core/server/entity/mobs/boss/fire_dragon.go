package boss

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/bossbar"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/dragonfly/server/world/particle"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"math/rand"
	"server/server/blocks/vanilla"
	"server/server/database"
	"server/server/factions/items"
	"server/server/utils"
	"time"
)

func NewFireDragon(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	d := &Dragon{
		Tier:              1 + rand.Intn(5),
		DragonIdentifier:  "custom:fire_dragon",
		BreathingParticle: particle.Flame{},
	}

	amount := []int{5 + rand.Intn(21)*1000, 50 + rand.Intn(41)*1000, 100 + rand.Intn(101)*1000, 300 + rand.Intn(101)*1000, 500 + rand.Intn(101)*1000}[d.Tier-1]
	chosenRank := database.Player
	for _, rnk := range []database.Rank{database.MGP, database.MLP, database.MMP, database.MVP, database.VIP} {
		if utils.RandChance(35) {
			chosenRank = rnk
			break
		}
	}

	conf := living.Config{
		EntityType: d,
		MaxHealth:  float64(200 * d.Tier),
		Speed:      1,
		Drops: []living.Drop{
			living.NewDropWithStack(items.BankNote{Amount: float64(amount)}.Stack()),
			living.NewDropWithStack(items.Kit{Type: chosenRank}.Stack()),
			living.NewDropWithStack(item.NewStack(item.BottleOfEnchanting{}, 300)),
		},
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.01,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Handler: &DragonHandler{
			D: d,
			otherBehavior: func(l *living.Living, tx *world.Tx) {
				if d.tick%(100*time.Millisecond) == 0 {
					for e := range tx.Players() {
						pl, _ := e.(*player.Player)
						if utils.Distance(pl.Position(), l.Position()) < 64 {
							newPos := pl.Position().Add(mgl64.Vec3{float64(-2 + rand.Intn(4)), -1, float64(-2 + rand.Intn(4))})
							oldB := tx.Block(cube.PosFromVec3(newPos))
							blocks := []world.Block{vanilla.Magma{}, block.Netherrack{}, block.Fire{Type: []block.FireType{block.NormalFire(), block.SoulFire()}[rand.Intn(2)]}}
							tx.SetBlock(cube.PosFromVec3(newPos), blocks[rand.Intn(len(blocks))], nil)
							time.AfterFunc(20*time.Second, func() {
								l.H().ExecWorld(func(tx *world.Tx, _ world.Entity) {
									tx.SetBlock(cube.PosFromVec3(newPos), oldB, nil)
								})
							})
						}
					}
				}
			},
		},
	}

	l := tx.AddEntity(world.EntitySpawnOpts{
		NameTag:  text.Colourf("<gold>[<dark-red>%v</dark-red>]</gold> <bold><red>Fire Dragon</red></bold>", d.Tier),
		Position: pos,
	}.New(conf.EntityType, conf)).(*living.Living)
	d.BossBar = bossbar.New(l.NameTag()).WithColour(bossbar.Red()).WithHealthPercentage(1)
	return l
}
