package mobs

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"time"
)

type Skeleton struct {
	living.NopLivingType

	headDropper
	shooter
}

func NewSkeleton(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	m := Skeleton{}
	timeNow := time.Now()
	conf := living.Config{
		EntityType: m,
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.7,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Speed:          0.1,
		EyeHeight:      0.5,
		MaxHealth:      20,
		Drops:          []living.Drop{living.NewDrop(item.Bone{}, 3, 4)},
		ImmuneDuration: 350 * time.Millisecond,
		Handler: &MobHandler{
			mob:       m,
			targetPos: &pos,
			shotAt:    &timeNow,
		},
	}
	l := tx.AddEntity(world.EntitySpawnOpts{NameTag: "<green>Skeleton</green>", Position: pos}.New(conf.EntityType, conf)).(*living.Living)
	RefreshName(l, l.Health())
	return l
}

func (Skeleton) EncodeEntity() string {
	return "minecraft:skeleton"
}
func (Skeleton) BBox(world.Entity) cube.BBox {
	return cube.Box(-0.3, 0, -0.3, 0.3, 1.9, 0.3)
}

func (Skeleton) Type() MobHead {
	return MobHead{SkullType: block.SkeletonSkull()}
}

func (Skeleton) ShootingRange() float64 {
	return 15
}

func (Skeleton) ShootingSpeed() time.Duration {
	return 3 * time.Second
}

func (Skeleton) Projectile(l *living.Living) *world.EntityHandle {
	return entity.NewArrowWithDamage(world.EntitySpawnOpts{
		Position: l.Position().Add(mgl64.Vec3{0, l.EyeHeight() + 1, 0}),
		Rotation: l.Rotation(),
	}, 3, l)
}

func (Skeleton) ProjectileSpeedMultiplier() float64 {
	return 1.75
}
