package mobs

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"time"
)

type SnowGolem struct {
	living.NopLivingType

	headDropper
	shooter
}

func NewSnowGolem(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	m := SnowGolem{}
	timeNow := time.Now()
	conf := living.Config{
		EntityType: m,
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.7,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Speed:          0.1,
		EyeHeight:      0.4,
		MaxHealth:      4,
		Drops:          []living.Drop{living.NewDrop(item.Snowball{}, 7, 8), living.NewDrop(block.Carrot{}, 0, 1)},
		ImmuneDuration: 350 * time.Millisecond,
		Handler: &MobHandler{
			mob:       m,
			targetPos: &pos,
			shotAt:    &timeNow,
		},
	}
	l := tx.AddEntity(world.EntitySpawnOpts{NameTag: "<green>Snow Golem</green>", Position: pos}.New(conf.EntityType, conf)).(*living.Living)
	RefreshName(l, l.Health())
	return l
}

func (SnowGolem) EncodeEntity() string {
	return "minecraft:snow_golem"
}
func (SnowGolem) BBox(world.Entity) cube.BBox {
	return cube.Box(-0.2, 0, -0.2, 0.2, 1.8, 0.2)
}

func (SnowGolem) Type() MobHead {
	return MobHead{Block: block.Pumpkin{}}
}

func (SnowGolem) ShootingRange() float64 {
	return 5
}

func (SnowGolem) ShootingSpeed() time.Duration {
	return 50 * time.Millisecond
}

func (SnowGolem) Projectile(l *living.Living) *world.EntityHandle {
	return entity.NewSnowball(world.EntitySpawnOpts{Position: l.Position().Add(mgl64.Vec3{0, l.EyeHeight() + 1, 0})}, l)
}

func (SnowGolem) ProjectileSpeedMultiplier() float64 {
	return 0.5
}
