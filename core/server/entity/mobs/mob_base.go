package mobs

import (
	"fmt"
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/dragonfly/server/world/particle"
	"github.com/df-mc/dragonfly/server/world/sound"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"image/color"
	"math/rand"
	"server/server/factions/enchants"
	"server/server/utils"
	"strings"
	"time"
)

type roamer interface {
	Roaming() bool
}
type coward interface {
	CowardSpeed() float64
	TimeTillCalm() time.Duration
}
type attacker interface {
	AttackRange() float64
	Damage(pl *player.Player) float64
}
type shooter interface {
	ShootingRange() float64
	ShootingSpeed() time.Duration
	Projectile(l *living.Living) *world.EntityHandle
	ProjectileSpeedMultiplier() float64
}
type hopper interface {
	JumpVelocity() float64
}

type headDropper interface {
	Type() MobHead
}

func SpawnEntity(pos mgl64.Vec3, w *world.Tx) {
	m := []func(){
		func() { NewChicken(pos, w) },
		func() { NewEnderman(pos, w) },
		func() { NewIronGolem(pos, w) },
		func() { NewPillager(pos, w) },
		func() { NewPolarBear(pos, w) },
		func() { NewSkeleton(pos, w) },
		func() { NewSlime(pos, w) },
		func() { NewSnowGolem(pos, w) },
		func() { NewWitch(pos, w) },
		func() { NewWitherSkeleton(pos, w) },
		func() { NewZombie(pos, w) },
	}
	w.AddParticle(pos, particle.Dust{Colour: color.RGBA{R: 255, G: 255, B: 255}})
	for e := range w.Entities() {
		if pl, ok := e.(*player.Player); ok && utils.Distance(pl.Position(), pos) < 20 {
			pl.PlaySound(sound.FireExtinguish{})
		}
	}
	m[rand.Intn(len(m))]()
}

func RefreshName(l *living.Living, health float64) {
	l.SetNameTag(text.Colourf("%v\n<red>%v ❤</red>", strings.Split(l.NameTag(), "\n")[0], fmt.Sprintf("%.1f", health)), l.Tx())
}

type MobHandler struct {
	living.NopHandler
	mob interface{}

	targetPos     *mgl64.Vec3
	shotAt        *time.Time
	otherBehavior func(l *living.Living, targetPos *mgl64.Vec3, shotAt *time.Time, tx *world.Tx)
}

func (h MobHandler) HandleTick(ctx living.Context, tx *world.Tx) {
	l := ctx.Val()

	if l.Dead() {
		return
	}

	if l.Age() > 10*time.Minute {
		l.Hurt(1000, entity.SuffocationDamageSource{})
		return
	}

	defaultJumpVelocity := 0.4
	skipRoaming := false
	pl, _ := utils.NearestPlayer(l, tx)
	if pl != nil {
		if hopper, ok := h.mob.(hopper); ok {
			utils.HopToTarget(l, hopper.JumpVelocity(), pl.Position(), tx)
			l.LookAt(pl.Position(), tx)
		} else if s, ok := h.mob.(shooter); ok {
			l.LookAt(pl.Position(), tx)
			if utils.Distance(l.Position(), pl.Position()) > s.ShootingRange() {
				skipRoaming = true
				l.MoveToTarget(pl.Position(), defaultJumpVelocity, tx)
			} else if time.Now().Sub(*h.shotAt) > s.ShootingSpeed() {
				skipRoaming = true
				offsetDistance := 0.5
				if utils.Distance(l.Position(), pl.Position()) > 10 {
					offsetDistance = 1.5
				}
				l.LookAt(pl.Position().Add(mgl64.Vec3{0, offsetDistance, 0}), tx)
				proj := tx.AddEntity(s.Projectile(l))
				proj.(*entity.Ent).SetVelocity(l.Rotation().Vec3().Mul(s.ProjectileSpeedMultiplier()))
				*h.shotAt = time.Now()
			}
		} else if a, ok := h.mob.(attacker); ok {
			l.LookAt(pl.Position(), tx)
			if utils.Distance(l.Position(), pl.Position()) > a.AttackRange() {
				if !l.AttackImmune() {
					l.MoveToTarget(pl.Position(), 0.4, tx)
				}
			} else {
				pl.Hurt(a.Damage(pl), entity.AttackDamageSource{Attacker: l})
				pl.KnockBack(l.Position(), 0.4, 0.4)
			}
		}
	}

	if _, ok := h.mob.(roamer); ok && !skipRoaming {
		if hopper, ok := h.mob.(hopper); ok {
			utils.HopToTarget(l, hopper.JumpVelocity(), *h.targetPos, tx)
			l.LookAt(*h.targetPos, tx)
		} else if !l.AttackImmune() && utils.Distance(l.Position(), *h.targetPos) > 0.5 {
			l.MoveToTarget(*h.targetPos, defaultJumpVelocity, tx)
		}

		if c, ok := h.mob.(coward); ok && l.AttackImmune() && l.Speed() != c.CowardSpeed() {
			oldS := l.Speed()
			l.SetSpeed(c.CowardSpeed())
			time.AfterFunc(c.TimeTillCalm(), func() {
				l.SetSpeed(oldS)
			})
		}

		if c, ok := h.mob.(coward); (ok && l.Speed() == c.CowardSpeed() && int(l.Age().Milliseconds())%((1+rand.Intn(1))*int(time.Second.Milliseconds())) == 0) || int(l.Age().Milliseconds())%((5+rand.Intn(4))*int(time.Second.Milliseconds())) == 0 {
			*h.targetPos = utils.SetVecY(l.Position().Add(mgl64.Vec3{float64(-3 + rand.Intn(6)), 0, float64(-3 + rand.Intn(6))}), l.Position().Y())
			l.LookAt(*h.targetPos, tx)
		}
	}

	if h.otherBehavior != nil {
		h.otherBehavior(l, h.targetPos, h.shotAt, tx)
	}
}

func (h MobHandler) HandleHurt(ctx living.Context, damage float64, _ bool, _ *time.Duration, src world.DamageSource) {
	l := ctx.Val()

	if _, ok := src.(entity.FallDamageSource); ok {
		ctx.Cancel()
		return
	}

	displayHealth := l.Health() - damage
	if l.Health() < damage {
		displayHealth = 0
	}
	RefreshName(l, displayHealth)
	if displayHealth == 0 {
		if s, ok := src.(entity.AttackDamageSource); ok {
			if killer, ok := s.Attacker.(*player.Player); ok {
				main, _ := killer.HeldItems()
				if enchant, ok := main.Enchantment(enchants.Decapitation{}); ok {
					if hd, ok := h.mob.(headDropper); ok {
						if utils.RandChance(float64(25 * enchant.Level())) {
							opts := world.EntitySpawnOpts{Position: l.Position()}
							l.Tx().AddEntity(entity.NewItem(opts, hd.Type().Stack()))
						}
					}
				}
			}
		}
	}
}
