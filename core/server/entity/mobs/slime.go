package mobs

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"server/server/blocks/vanilla"
	"time"
)

type Slime struct {
	living.NopLivingType

	headDropper
	hopper
	attacker
}

func NewSlime(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	m := Slime{}
	timeNow := time.Now()
	conf := living.Config{
		EntityType: m,
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.7,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Speed:          0.3,
		EyeHeight:      0.4,
		MaxHealth:      1,
		Drops:          []living.Drop{living.NewDrop(item.Emerald{}, 1, 2), living.NewDrop(item.GoldIngot{}, 1, 2)},
		ImmuneDuration: 350 * time.Millisecond,
		Handler: &MobHandler{
			mob:       m,
			targetPos: &pos,
			shotAt:    &timeNow,
		},
	}
	l := tx.AddEntity(world.EntitySpawnOpts{NameTag: "<green>Slime</green>", Position: pos}.New(conf.EntityType, conf)).(*living.Living)
	RefreshName(l, l.Health())
	return l
}

func (Slime) EncodeEntity() string {
	return "minecraft:slime"
}
func (Slime) BBox(world.Entity) cube.BBox {
	return cube.Box(-0.25, 0, -0.25, 0.5, 0.25, 0.25)
}

func (Slime) Type() MobHead {
	return MobHead{Block: vanilla.Slime{}}
}

func (Slime) JumpVelocity() float64 {
	return 0.4
}

func (Slime) AttackRange() float64 {
	return 1.5
}

func (Slime) Damage(*player.Player) float64 {
	return 2
}
