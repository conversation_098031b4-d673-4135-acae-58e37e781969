package mobs

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"server/server/blocks/heads/chicken"
	"server/server/utils"
	"time"
)

type Chicken struct {
	living.NopLivingType

	headDropper
	roamer
	coward
}

func NewChicken(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	m := Chicken{}
	timeNow := time.Now()
	conf := living.Config{
		EntityType: m,
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.05,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Speed:          0.2,
		EyeHeight:      0.1,
		MaxHealth:      4,
		Drops:          []living.Drop{living.NewDrop(item.Feather{}, 4, 5), living.NewDrop(item.Chicken{}, 1, 2)},
		ImmuneDuration: 350 * time.Millisecond,
		Handler: &MobHandler{
			mob:       m,
			targetPos: &pos,
			shotAt:    &timeNow,
			otherBehavior: func(l *living.Living, targetPos *mgl64.Vec3, _ *time.Time, tx *world.Tx) {
				nearestPl, minD := utils.NearestPlayer(l, tx)
				if nearestPl != nil && minD < 10 && minD > 1 {
					main, off := nearestPl.HeldItems()
					_, ok1 := main.Item().(block.WheatSeeds)
					_, ok2 := off.Item().(block.WheatSeeds)
					if ok1 || ok2 {
						l.MoveToTarget(nearestPl.Position(), 1, tx) // 0.1
						l.LookAt(nearestPl.Position(), tx)
						*targetPos = l.Position()
					}
				}
			},
		},
	}
	l := tx.AddEntity(world.EntitySpawnOpts{NameTag: "<green>Chicken</green>", Position: pos}.New(conf.EntityType, conf)).(*living.Living)
	RefreshName(l, l.Health())
	return l
}

func (Chicken) EncodeEntity() string {
	return "minecraft:chicken"
}
func (Chicken) BBox(world.Entity) cube.BBox {
	return cube.Box(-0.25, 0, -0.4, 0.25, 0.8, 0.4)
}

func (Chicken) Type() MobHead {
	return MobHead{Block: chicken.Head{}}
}

func (c Chicken) CowardSpeed() float64 {
	return 0.4
}

func (c Chicken) TimeTillCalm() time.Duration {
	return 4 * time.Second
}

func (Chicken) Roaming() bool {
	return true
}
