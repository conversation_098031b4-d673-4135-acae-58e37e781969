package mobs

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"time"
)

type IronGolem struct {
	living.NopLivingType

	headDropper
	attacker
}

func NewIronGolem(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	m := IronGolem{}
	timeNow := time.Now()
	conf := living.Config{
		EntityType: m,
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.7,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Speed:          0.1,
		EyeHeight:      1.5,
		MaxHealth:      100,
		Drops:          []living.Drop{living.NewDrop(item.IronIngot{}, 6, 7), living.NewDrop(block.DoubleFlower{Type: block.RoseBush()}, 1, 2)},
		ImmuneDuration: 350 * time.Millisecond,
		Handler: &MobHandler{
			mob:       m,
			targetPos: &pos,
			shotAt:    &timeNow,
		},
	}
	l := tx.AddEntity(world.EntitySpawnOpts{NameTag: "<green>Iron Golem</green>", Position: pos}.New(conf.EntityType, conf)).(*living.Living)
	RefreshName(l, l.Health())
	return l
}

func (IronGolem) EncodeEntity() string {
	return "minecraft:iron_golem"
}
func (IronGolem) BBox(world.Entity) cube.BBox {
	return cube.Box(-0.7, 0, -0.7, 0.7, 2.9, 0.7)
}

func (IronGolem) Type() MobHead {
	return MobHead{Block: block.Iron{}}
}

func (IronGolem) AttackRange() float64 {
	return 1.5
}

func (IronGolem) Damage(*player.Player) float64 {
	return 3.5
}
