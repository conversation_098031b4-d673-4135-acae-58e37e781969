package mobs

import (
	"github.com/bedrock-gophers/living/living"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"server/server/blocks/heads/pillager"
	"time"
)

type Pillager struct {
	living.NopLivingType

	headDropper
	attacker
}

func NewPillager(pos mgl64.Vec3, tx *world.Tx) *living.Living {
	m := Pillager{}
	timeNow := time.Now()
	conf := living.Config{
		EntityType: m,
		MovementComputer: &entity.MovementComputer{
			Gravity:           0.7,
			Drag:              0.02,
			DragBeforeGravity: true,
		},
		Speed:          0.1,
		EyeHeight:      0.5,
		MaxHealth:      24,
		Drops:          []living.Drop{living.NewDrop(item.Emerald{}, 0, 1), living.NewDrop(item.RottenFlesh{}, 1, 2)},
		ImmuneDuration: 350 * time.Millisecond,
		Handler: &MobHandler{
			mob:       m,
			targetPos: &pos,
			shotAt:    &timeNow,
		},
	}
	l := tx.AddEntity(world.EntitySpawnOpts{NameTag: "<green>Pillager</green>", Position: pos}.New(conf.EntityType, conf)).(*living.Living)
	RefreshName(l, l.Health())
	return l
}

func (Pillager) EncodeEntity() string {
	return "minecraft:pillager"
}
func (Pillager) BBox(world.Entity) cube.BBox {
	return cube.Box(-0.3, 0, -0.3, 0.3, 1.9, 0.3)
}

func (Pillager) Type() MobHead {
	return MobHead{Block: pillager.Head{}}
}

func (Pillager) AttackRange() float64 {
	return 1.5
}
func (Pillager) Damage(*player.Player) float64 {
	return 3
}
