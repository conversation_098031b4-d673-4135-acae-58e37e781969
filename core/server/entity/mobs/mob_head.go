package mobs

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"server/server/blocks"
	"server/server/blocks/heads/chicken"
	"server/server/blocks/heads/enderman"
	"server/server/blocks/heads/pillager"
	"server/server/blocks/heads/polar_bear"
	"server/server/blocks/heads/witch"
	"server/server/blocks/vanilla"
	"server/server/factions/enchants"
	"server/server/factions/spawner"
	"server/server/language"
)

func init() {
	blocks.RegisterSpecialItem(blocks.MobHead, MobHead{})
}

type MobHead struct {
	world.Item
	Block     world.Item
	SkullType block.SkullType
}

func (m MobHead) Stack() item.Stack {
	var s item.Stack
	if m.Block != nil {
		s = item.NewStack(m.Block, 1)
	} else {
		s = item.NewStack(block.Skull{Type: m.SkullType}, 1)
	}
	s = s.WithValue("special_item", int16(blocks.MobHead)).WithEnchantments(item.NewEnchantment(enchants.Glitter{}, 1))
	s = s.WithLore("Click on a spawner to make the spawner summon the mob")
	return s
}

func (MobHead) UseOnBlock(pos cube.Pos, face cube.Face, _ mgl64.Vec3, tx *world.Tx, usr item.User, ctx *item.UseContext) (used bool) {
	pl := usr.(*player.Player)
	b := pl.Tx().Block(pos)
	if _, ok := b.(spawner.Spawner); ok {
		if main, _ := pl.HeldItems(); headToEgg(main) != nil {
			activateSpawner(pl, pos)
		} else {
			pl.Message(language.Translate(pl).Error.NotSpawnerItem)
		}
	}

	ctx.CountSub = 1
	return true
}

func activateSpawner(pl *player.Player, spawnerPos cube.Pos) {
	main, off := pl.HeldItems()

	pl.Tx().SetBlock(spawnerPos, spawner.Spawner{
		EntityType:          headToEgg(main).Kind,
		Delay:               15 * 20,
		Movable:             true,
		RequiredPlayerRange: 32,
		MaxNearbyEntities:   10,
		MaxSpawnDelay:       35 * 20,
		MinSpawnDelay:       20 * 20,
		SpawnCount:          4,
		SpawnRange:          15,
	}, nil)

	pl.SetHeldItems(main.Grow(-1), off)
}

func headToEgg(stack item.Stack) *spawner.SpawnEgg {
	if s, ok := stack.Item().(block.Skull); ok {
		switch s.Type {
		case block.SkeletonSkull():
			return &spawner.SpawnEgg{Kind: Skeleton{}}
		case block.WitherSkeletonSkull():
			return &spawner.SpawnEgg{Kind: WitherSkeleton{}}
		case block.ZombieHead():
			return &spawner.SpawnEgg{Kind: Zombie{}}
		}
	} else {
		if stack.Comparable(MobHead{Block: block.Pumpkin{}}.Stack()) {
			return &spawner.SpawnEgg{Kind: SnowGolem{}}
		} else if stack.Comparable(MobHead{Block: chicken.Head{}}.Stack()) {
			return &spawner.SpawnEgg{Kind: Chicken{}}
		} else if stack.Comparable(MobHead{Block: enderman.Head{}}.Stack()) {
			return &spawner.SpawnEgg{Kind: Enderman{}}
		} else if stack.Comparable(MobHead{Block: pillager.Head{}}.Stack()) {
			return &spawner.SpawnEgg{Kind: Pillager{}}
		} else if stack.Comparable(MobHead{Block: polar_bear.Head{}}.Stack()) {
			return &spawner.SpawnEgg{Kind: PolarBear{}}
		} else if stack.Comparable(MobHead{Block: witch.Head{}}.Stack()) {
			return &spawner.SpawnEgg{Kind: Witch{}}
		} else if stack.Comparable(MobHead{Block: block.Iron{}}.Stack()) {
			return &spawner.SpawnEgg{Kind: IronGolem{}}
		} else if stack.Comparable(MobHead{Block: vanilla.Slime{}}.Stack()) {
			return &spawner.SpawnEgg{Kind: Slime{}}
		}
	}

	return nil
}
