package listeners

import (
	"anticheat/handlers"
	"fmt"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/item/enchantment"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/chat"
	"github.com/df-mc/dragonfly/server/player/title"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/dragonfly/server/world/sound"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/protocol"
	"github.com/sandertv/gophertunnel/minecraft/protocol/packet"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"log/slog"
	"math"
	"math/rand"
	"server/server"
	items2 "server/server/blocks"
	"server/server/database"
	"server/server/entity/mobs"
	serverFactions "server/server/factions"
	"server/server/factions/backpack"
	"server/server/factions/items"
	"server/server/factions/ui"
	"server/server/language"
	"server/server/user"
	"server/server/utils"
	"slices"
	"strings"
	"time"
)

type FactionHandler struct {
	player.NopHandler

	ACHandler *handlers.ACPlayerHandler
}

func (h FactionHandler) HandleHurt(ctx *player.Context, damage *float64, immune bool, attackImmunity *time.Duration, src world.DamageSource) {
	if h.ACHandler != nil {
		h.ACHandler.HandleHurt(ctx, damage, immune, attackImmunity, src)
	}

	pl := ctx.Val()
	u := user.GetUser(pl)

	if _, ok := src.(entity.FallDamageSource); ok {
		ctx.Cancel()
		return
	}

	// Cancel teleport timer when player takes any damage
	u.CancelTeleportTimer()

	c1 := server.Config.Hub.NoPvp.C1
	c2 := server.Config.Hub.NoPvp.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pl.Position()) {
		ctx.Cancel()
		pl.Message(text.Colourf(language.Translate(pl).Error.PvPDisabled))
		return
	}

	var killer world.Entity
	if s1, ok := src.(entity.AttackDamageSource); ok {
		killer = s1.Attacker
	} else if s2, ok := src.(entity.ProjectileDamageSource); ok {
		killer = s2.Owner
	}

	if killer, ok := killer.(*player.Player); ok {
		uk := user.GetUser(killer)
		if u.Data.Faction.HasFaction() && uk.Data.Faction.HasFaction() && u.Data.Faction.Faction() == uk.Data.Faction.Faction() {
			return
		}

		if !u.IsCoolDownActive(user.Combat, 15*time.Second, true, true, false) {
			killer.Message(text.Colourf(language.Translate(killer).CombatLogged))
			pl.Message(text.Colourf(language.Translate(pl).CombatLogged))
		}

		// Cancel any active teleport timers for both players when combat starts
		u.CancelTeleportTimer()
		uk.CancelTeleportTimer()
	}

	if pl.Health()+pl.Absorption() <= *damage {
		up := user.GetUser(pl)
		up.Data.Faction.Stats.Deaths++
		up.Data.Faction.Stats.BestKillStreak = max(up.Data.Faction.Stats.KillStreak, up.Data.Faction.Stats.BestKillStreak)
		up.Data.Faction.Stats.KillStreak = 0

		performFactionDeath(pl)

		if _, err := pl.Inventory().AddItem(backpack.Backpack{}.Stack()); err == nil {
			pl.Message(text.Colourf(language.Translate(pl).GiveBackpack, server.Config.Prefix, backpack.Backpack{}.Stack().CustomName()))
		}

		pl.Heal(pl.MaxHealth(), nil)
		pl.SetFood(20)
		for _, e := range pl.Effects() {
			pl.RemoveEffect(e.Type())
		}
		pl.Extinguish()
		pl.RemoveExperience(pl.Experience())
		pl.RemoveBossBar()
		pl.SendTitle(title.New(text.Colourf(language.Translate(pl).YouDied)))
		if killer, ok := killer.(*player.Player); ok {
			uk := user.GetUser(killer)
			uk.Data.Faction.Stats.Strength += 0.5
			if uk.Data.Faction.HasFaction() {
				uk.Data.Faction.Faction().Strength += 5
			}
			uk.Data.Faction.Stats.Kills++
			uk.Data.Faction.Stats.KillStreak++
			if _, err := killer.Inventory().AddItem(items.PlayerHead{KilledUuid: pl.UUID()}.Stack()); err != nil {
				killer.Message(text.Colourf(language.Translate(killer).Error.InventoryFull))
			}

			if up.Data.Faction.Stats.Strength >= 0.5 {
				up.Data.Faction.Stats.Strength -= 0.5
			}
			if up.Data.Faction.HasFaction() && up.Data.Faction.Faction().Strength >= 10 {
				up.Data.Faction.Faction().Strength -= 10
			}
		}
		ctx.Cancel()
	}

	EnchantsHandleHurt(ctx, damage, attackImmunity, src)
}

func performFactionDeath(pl *player.Player) {
	pos := pl.Position()
	for _, orb := range entity.NewExperienceOrbs(pos, int(math.Min(float64(pl.ExperienceLevel()*7), 100))) {
		pl.Tx().AddEntity(orb)
	}
	pl.SetExperienceLevel(0)
	utils.Session(pl).SendExperience(pl.ExperienceLevel(), pl.ExperienceProgress())

	pl.MoveItemsToInventory()
	for _, it := range append(pl.Inventory().Clear(), pl.Armour().Clear()...) {
		if _, ok := it.Enchantment(enchantment.CurseOfVanishing); ok {
			continue
		}
		opts := world.EntitySpawnOpts{Position: pos, Velocity: mgl64.Vec3{rand.Float64()*0.2 - 0.1, 0.2, rand.Float64()*0.2 - 0.1}}
		pl.Tx().AddEntity(entity.NewItem(opts, it))
	}

	pl.Teleport(server.Config.Hub.SpawnPoint)
}

func (h FactionHandler) HandleAttackEntity(ctx *player.Context, e world.Entity, force, height *float64, critical *bool) {
	if h.ACHandler != nil {
		h.ACHandler.HandleAttackEntity(ctx, e, force, height, critical)
	}
}

func (h FactionHandler) HandleQuit(pl *player.Player) {
	if h.ACHandler != nil {
		h.ACHandler.HandleQuit(pl)
	}

	u := user.GetUser(pl)
	u.Data.LastLogin = time.Now()
	u.Data.Online = false

	// Cancel any active teleport timer when player quits
	u.CancelTeleportTimer()

	// Save Dragonfly player data (inventory, position, health, etc.) first
	if err := server.DragonflyConfig.PlayerProvider.Save(pl.UUID(), pl.Data(), server.MCServer.World()); err != nil {
		errorCode := utils.RandString(6)
		slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save Dragonfly player data on quit: " + err.Error())
	}

	// Save custom player data (faction data, stats, etc.)
	if err := user.Save(pl); err != nil {
		// Log the error but don't crash the server
		errorCode := utils.RandString(6)
		slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data on quit: " + err.Error())
	}

	if u.CoolDownTimeRemaining(user.Combat) > 0 {
		pl.Hurt(10000, entity.VoidDamageSource{})
	}
}

func (h FactionHandler) HandleChat(ctx *player.Context, msg *string) {
	if h.ACHandler != nil {
		h.ACHandler.HandleChat(ctx, msg)
	}

	pl := ctx.Val()
	u := user.GetUser(pl)

	ctx.Cancel()
	*msg = text.Colourf("%v<white>:</white> %v", user.FactionNameDisplay.Name(u.Data), *msg)
	_, _ = fmt.Fprintf(chat.Global, *msg)
}

func CheckPlayerChangeTerritory(u *user.User, pos cube.Pos) bool {
	facWithin := database.FactionWithin(pos.Vec3())
	if facWithin != nil {
		if u.Data.Faction.HasFaction() && u.Data.Faction.Name != facWithin.Name {
			return false
		} else {
			return u.Data.Faction.Stats.Strength > facWithin.Strength
		}
	}
	return true
}

func (h FactionHandler) HandleItemUse(ctx *player.Context) {
	if h.ACHandler != nil {
		h.ACHandler.HandleItemUse(ctx)
	}

	pl := ctx.Val()
	u := user.GetUser(pl)
	if u.IsCoolDownActive(user.Use, 50*time.Millisecond, false, true, false) {
		return
	}

	main, off := pl.HeldItems()
	if v, ok := main.Value("special_item"); ok && main.Count() != 0 {
		if _, ok := main.Value("wild_only"); ok {
			if !CheckPlayerChangeTerritory(user.GetUser(pl), cube.PosFromVec3(pl.Position())) {
				pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pl.Position()).Name))
				ctx.Cancel()
				return
			}

			c1 := server.Config.Hub.BlockProtectionZone.C1
			c2 := server.Config.Hub.BlockProtectionZone.C2
			box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
			if box.Vec3Within(pl.Position()) && pl.GameMode() != world.GameModeCreative {
				pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
				ctx.Cancel()
				return
			}
		}

		if uit, ok := items2.SpecialItem(items2.ItemAction(v.(int16))).(item.Usable); ok {
			ctx.Cancel()

			useCtx := item.UseContext{}
			uit.Use(pl.Tx(), pl, &useCtx)
			if useCtx.CountSub > 0 {
				pl.SetHeldItems(main.Grow(-1*useCtx.CountSub), off)
			}
		}
	}
}

func (h FactionHandler) HandleItemUseOnBlock(ctx *player.Context, pos cube.Pos, face cube.Face, clickPos mgl64.Vec3) {
	if h.ACHandler != nil {
		h.ACHandler.HandleItemUseOnBlock(ctx, pos, face, clickPos)
	}

	pl := ctx.Val()
	u := user.GetUser(pl)
	if u.IsCoolDownActive(user.Use, 50*time.Millisecond, false, true, false) {
		return
	}

	main, off := pl.HeldItems()
	if v, ok := main.Value("special_item"); ok && main.Count() != 0 {
		if _, ok := main.Value("wild_only"); ok {
			if v, ok := main.Value("special_item"); (!ok || v != int16(items2.ClaimShovel)) && !CheckPlayerChangeTerritory(user.GetUser(pl), cube.PosFromVec3(pl.Position())) {
				pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pl.Position()).Name))
				ctx.Cancel()
				return
			}

			c1 := server.Config.Hub.BlockProtectionZone.C1
			c2 := server.Config.Hub.BlockProtectionZone.C2
			box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
			if box.Vec3Within(pl.Position()) && pl.GameMode() != world.GameModeCreative {
				pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
				ctx.Cancel()
				return
			}
		}

		if uit, ok := items2.SpecialItem(items2.ItemAction(v.(int16))).(item.UsableOnBlock); ok {
			ctx.Cancel()

			useCtx := item.UseContext{}
			uit.UseOnBlock(pos, face, clickPos, pl.Tx(), pl, &useCtx)
			if useCtx.CountSub > 0 {
				pl.SetHeldItems(main.Grow(-1*useCtx.CountSub), off)
			}
		}
	}

	if !CheckPlayerChangeTerritory(user.GetUser(pl), pos) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pos.Vec3()).Name))
		ctx.Cancel()
		return
	}

	b := pl.Tx().Block(pos)
	if _, ok := b.(block.Chest); ok {
		var ct *database.CrateType
		for _, t := range []database.CrateType{database.Common, database.Rare, database.Legendary, database.Ancient, database.Vote} {
			if equal(pos.Vec3(), server.Config.Hub.Crates[t], 0.5) {
				ct = &t
				break
			}
		}

		if ct != nil {
			ctx.Cancel()
			if *ct == database.Vote {
				if u.Data.Faction.Stats.CrateKeys[database.Vote] > 0 {
					u.Data.Faction.Stats.CrateKeys[database.Vote]--
					u.Data.Faction.Stats.CrateKeys[database.Common] += 4
					u.Data.Faction.Stats.CrateKeys[database.Rare] += 4
					u.Data.Faction.Stats.CrateKeys[database.Legendary] += 4
					u.Data.Faction.Stats.CrateKeys[database.Ancient] += 4
					pl.Message(text.Colourf(language.Translate(pl).Give16Keys, server.Config.Prefix, database.Common.Name(), database.Rare.Name(), database.Legendary.Name(), database.Ancient.Name()))
				} else {
					pl.Message(text.Colourf(language.Translate(pl).Error.NoVoteKeys))
				}
			} else {
				// Simple crate opening - no UI needed
				if u.Data.Faction.Stats.CrateKeys[*ct] > 0 {
					u.Data.Faction.Stats.CrateKeys[*ct]--

					// Get a random reward
					reward := ui.Roll(*ct)

					// Give the reward directly to player
					if _, err := pl.Inventory().AddItem(reward); err != nil {
						pl.Drop(reward)
						pl.Message(text.Colourf("<yellow>✦ Crate reward dropped (inventory full)</yellow>"))
					}

					// Play success sound
					pl.PlaySound(sound.LevelUp{})

					// Update the crate display to show what was won
					go func() {
						// Show the reward for 3 seconds
						time.Sleep(100 * time.Millisecond) // Small delay to ensure the crate display updates
						updateCrateDisplayWithReward(pl, *ct, reward)

						// Reset to normal display after 3 seconds
						time.Sleep(3 * time.Second)
						updateCrateDisplayNormal(pl, *ct)
					}()
				} else {
					pl.Message(text.Colourf("<red>✗ You don't have any %v <red>keys!</red></red>", ct.Name()))
					pl.PlaySound(sound.Deny{})
				}
			}
			return
		}
	}

	c1 := server.Config.Hub.BlockProtectionZone.C1
	c2 := server.Config.Hub.BlockProtectionZone.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pos.Vec3()) && pl.GameMode() != world.GameModeCreative {
		pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
		ctx.Cancel()
		return
	}

	if _, ok := main.Item().(item.Firework); ok {
		if u.CoolDownTimeRemaining(user.Combat) > 0 {
			ctx.Cancel()
		}
	}
}

func (h FactionHandler) HandleItemUseOnEntity(ctx *player.Context, e world.Entity) {
	if h.ACHandler != nil {
		h.ACHandler.HandleItemUseOnEntity(ctx, e)
	}

	pl := ctx.Val()

	if !CheckPlayerChangeTerritory(user.GetUser(pl), cube.PosFromVec3(e.Position())) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(e.Position()).Name))
		ctx.Cancel()
		return
	}

	c1 := server.Config.Hub.BlockProtectionZone.C1
	c2 := server.Config.Hub.BlockProtectionZone.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(e.Position()) && pl.GameMode() != world.GameModeCreative {
		pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
		ctx.Cancel()
		return
	}
}

func (h FactionHandler) HandleHeldSlotChange(ctx *player.Context, from, to int) {
	if h.ACHandler != nil {
		h.ACHandler.HandleHeldSlotChange(ctx, from, to)
	}

	pl := ctx.Val()
	u := user.GetUser(pl)
	toStack, _ := pl.Inventory().Item(to)
	fromStack, _ := pl.Inventory().Item(from)

	if v, ok := toStack.Value("special_item"); ok && v == int16(items2.ClaimShovel) && u.Data.Faction.HasFaction() {
		u.Data.Faction.Faction().RefreshChunkLines(pl)
	} else if v, ok := fromStack.Value("special_item"); ok && v == int16(items2.ClaimShovel) && u.Data.Faction.HasFaction() {
		utils.Session(pl).RemoveAllDebugShapes()
	}
}

func (h FactionHandler) HandleStartBreak(ctx *player.Context, pos cube.Pos) {
	if h.ACHandler != nil {
		h.ACHandler.HandleStartBreak(ctx, pos)
	}

	pl := ctx.Val()

	main, off := pl.HeldItems()
	if v, ok := main.Value("special_item"); ok {
		ctx.Cancel()

		if uit, ok := items2.SpecialItem(items2.ItemAction(v.(int16))).(ActivatedOnStartBreak); ok {
			ctx.Cancel()

			useCtx := item.UseContext{}
			uit.OnStartBreak(pl, pos)
			if useCtx.CountSub > 0 {
				pl.SetHeldItems(main.Grow(-1*useCtx.CountSub), off)
			}
		}
	}

	if !CheckPlayerChangeTerritory(user.GetUser(pl), pos) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pos.Vec3()).Name))
		ctx.Cancel()
		return
	}
}

func (h FactionHandler) HandleBlockBreak(ctx *player.Context, pos cube.Pos, drops *[]item.Stack, xp *int) {
	if h.ACHandler != nil {
		h.ACHandler.HandleBlockBreak(ctx, pos, drops, xp)
	}

	pl := ctx.Val()
	u := user.GetUser(pl)

	if !CheckPlayerChangeTerritory(user.GetUser(pl), pos) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pos.Vec3()).Name))
		ctx.Cancel()
		return
	}

	c1 := server.Config.Hub.BlockProtectionZone.C1
	c2 := server.Config.Hub.BlockProtectionZone.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pos.Vec3()) && pl.GameMode() != world.GameModeCreative {
		pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
		ctx.Cancel()
		return
	}

	if !slices.Contains(u.FactionInfo.IgnoreDrilledBlocksAt, pos.Vec3()) {
		EnchantsHandleBlockBreak(ctx, pos, drops, xp)
	}
}

func (h FactionHandler) HandleBlockPlace(ctx *player.Context, pos cube.Pos, b world.Block) {
	if h.ACHandler != nil {
		h.ACHandler.HandleBlockPlace(ctx, pos, b)
	}

	pl := ctx.Val()

	if !CheckPlayerChangeTerritory(user.GetUser(pl), pos) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pos.Vec3()).Name))
		ctx.Cancel()
		return
	}

	c1 := server.Config.Hub.BlockProtectionZone.C1
	c2 := server.Config.Hub.BlockProtectionZone.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pos.Vec3()) && pl.GameMode() != world.GameModeCreative {
		pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
		ctx.Cancel()
		return
	}

	// Debounced save after block placement to prevent inventory loss
	user.DebouncedSave(pl)
}

func (h FactionHandler) HandleMove(ctx *player.Context, from mgl64.Vec3, to cube.Rotation) {
	if h.ACHandler != nil {
		h.ACHandler.HandleMove(ctx, from, to)
	}

	pl := ctx.Val()
	u := user.GetUser(pl)

	// Check if player has moved and cancel teleport timer if so
	u.CheckTeleportMovement(pl.Position())

	if u.CoolDownTimeRemaining(user.Combat) > 0 {
		if pl.Gliding() {
			pl.StopGliding()
			pl.SetVelocity(mgl64.Vec3{0, 0, 0})
		}

		if pl.Flying() {
			pl.StopFlying()
			pl.SetGameMode(world.GameModeSurvival)
			pl.SetVelocity(mgl64.Vec3{0, 0, 0})
		}
	}
}

func (h FactionHandler) HandleFoodLoss(ctx *player.Context, from int, to *int) {
	if h.ACHandler != nil {
		h.ACHandler.HandleFoodLoss(ctx, from, to)
	}

	pl := ctx.Val()

	// Check if player is in NoPvP zone
	c1 := server.Config.Hub.NoPvp.C1
	c2 := server.Config.Hub.NoPvp.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pl.Position()) {
		// Cancel hunger loss in NoPvP zones
		ctx.Cancel()
		return
	}
}

func HandlePlayerJoin(pl *player.Player) {
	u := user.GetUser(pl)
	u.Data.Online = true

	if pl.Name() == "AlphICEter" || pl.Name() == "Studgi" || pl.Name() == "ShotDead7297" {
		u.Data.RankId = database.Owner.Shortened()
	}

	// Perform immediate world operations on main thread
	pl.Teleport(server.Config.Hub.SpawnPoint)
	pl.SetNameTag(user.FactionNameDisplay.Name(u.Data))
	pl.SetGameMode(world.GameModeSurvival)
	pl.ShowCoordinates()

	giveMissingItems(pl)
	giveDailyDoubloons(pl)

	// Save player data asynchronously to avoid blocking
	go func() {
		if err := user.Save(pl); err != nil {
			errorCode := utils.RandString(6)
			slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save player data on join: " + err.Error())
		}
	}()

	// Start background tasks
	go updateCrateText(pl)
	go spawnMob(pl)
}

func giveMissingItems(pl *player.Player) {
	u := user.GetUser(pl)

	if u.Data.Faction.HasFaction() && u.Data.Faction.Role == database.Leader {
		if !pl.Inventory().ContainsItem(items.ClaimShovel{}.Stack()) {
			if _, err := pl.Inventory().AddItem(items.ClaimShovel{}.Stack()); err == nil {
				pl.Message(text.Colourf(language.Translate(pl).NoClaimArea, server.Config.Prefix, items.ClaimShovel{}.Stack().CustomName()))
			}
		}
	}

	if !pl.Inventory().ContainsItem(backpack.Backpack{}.Stack()) {
		if _, err := pl.Inventory().AddItem(backpack.Backpack{}.Stack()); err == nil {
			pl.Message(text.Colourf(language.Translate(pl).GiveBackpack, server.Config.Prefix, backpack.Backpack{}.Stack().CustomName()))
		}
	}
}

func giveDailyDoubloons(pl *player.Player) {
	u := user.GetUser(pl)
	offlineMul := (int)(time.Now().Sub(u.Data.LastLogin).Seconds() / (time.Hour * 24).Seconds())
	var dailyDoubloons int
	switch u.Data.Rank() {
	case database.MGP, database.MLP:
		dailyDoubloons = 500000
	case database.MMP:
		dailyDoubloons = 450000
	case database.MVP:
		dailyDoubloons = 350000
	case database.VIP:
		dailyDoubloons = 250000
	default:
		dailyDoubloons = 0
	}
	dailyDoubloons *= offlineMul
	u.Data.Faction.Stats.Doubloons += float64(dailyDoubloons)
	for i := 0; i < offlineMul; i++ {
		u.Data.Faction.Stats.CrateKeys[database.RandCrateType()]++
	}

	if dailyDoubloons != 0 {
		pl.Message(text.Colourf(language.Translate(pl).DailyDoubloons, server.Config.Prefix, dailyDoubloons, offlineMul))
	}
}

func updateCrateText(pl *player.Player) {
	for range time.NewTicker(5 * time.Second).C {
		u := user.GetUser(pl)
		if u == nil {
			break
		}
		serverFactions.SendMainScoreboard(pl)

		for t := range server.Config.Hub.Crates {
			p := server.Config.Hub.Crates[t].Add(mgl64.Vec3{0, 1.25, 0})
			pl.H().ExecWorld(func(tx *world.Tx, _ world.Entity) {
				for e := range tx.Entities() {
					if ent, ok := e.(*entity.Ent); ok {
						if ent.H().Type() == entity.TextType && ent.Position() == p {
							ct := database.CrateType(t)

							session := utils.Session(pl)
							md := utils.ParseEntityMetadata(session, e)
							// Get the crate color for the key count
							var keyColor string
							switch ct {
							case database.Common:
								keyColor = "grey"
							case database.Rare:
								keyColor = "blue"
							case database.Legendary:
								keyColor = "dark-purple"
							case database.Ancient:
								keyColor = "gold"
							case database.Vote:
								keyColor = "green"
							default:
								keyColor = "white"
							}

							// Special display for Vote crate with jackpot
							if ct == database.Vote {
								jackpot := database.GetJackpot()
								md[protocol.EntityDataKeyName] = text.Colourf("%v\n<grey>You have <%v>%v</%v> keys</grey>\n<gray>Jackpot</gray> <aqua>$%s</aqua>",
									ct.Name(), keyColor, u.Data.Faction.Stats.CrateKeys[ct], keyColor, utils.AddCommas(jackpot.Amount))
							} else {
								md[protocol.EntityDataKeyName] = text.Colourf("%v\n<grey>You have <%v>%v</%v> keys</grey>", ct.Name(), keyColor, u.Data.Faction.Stats.CrateKeys[ct], keyColor)
							}
							utils.WritePacket(session, &packet.SetActorData{
								EntityRuntimeID: utils.EntityRuntimeID(session, e),
								EntityMetadata:  md,
							})
						}
					}
				}
			})
		}
	}
}

func spawnMob(pl *player.Player) {
	for {
		time.Sleep((3 + time.Duration(rand.Intn(3))) * time.Minute)
		if utils.Distance(server.Config.Hub.SpawnPoint, pl.Position()) >= 500 {
			x := pl.Position().X() + float64(rand.Intn(15))
			z := pl.Position().Z() + float64(rand.Intn(15))
			pl.H().ExecWorld(func(tx *world.Tx, e world.Entity) {
				y := float64(tx.HighestBlock(int(x), int(z)) + 3)
				v := mgl64.Vec3{x, y, z}
				facWithin := database.FactionWithin(v)
				if facWithin == nil {
					mobs.SpawnEntity(v, tx)
				}
			})
		}
	}
}

func equal(v1, v2 mgl64.Vec3, tolerance float64) bool {
	return math.Abs(v1.X()-v2.X()) <= tolerance &&
		math.Abs(v1.Y()-v2.Y()) <= tolerance &&
		math.Abs(v1.Z()-v2.Z()) <= tolerance
}

type ActivatedOnStartBreak interface {
	OnStartBreak(pl *player.Player, pos cube.Pos) bool
}

// updateCrateDisplayWithReward shows what reward was won on the crate
func updateCrateDisplayWithReward(pl *player.Player, ct database.CrateType, reward item.Stack) {
	p := server.Config.Hub.Crates[ct].Add(mgl64.Vec3{0, 1.25, 0})

	pl.H().ExecWorld(func(tx *world.Tx, _ world.Entity) {
		for e := range tx.Entities() {
			if ent, ok := e.(*entity.Ent); ok {
				if ent.H().Type() == entity.TextType && ent.Position() == p {
					session := utils.Session(pl)
					md := utils.ParseEntityMetadata(session, e)

					// Show the reward with celebration
					rewardName := reward.CustomName()
					if rewardName == "" {
						// Convert raw item names to user-friendly names
						rewardName = getItemDisplayName(reward)
					}

					md[protocol.EntityDataKeyName] = text.Colourf("%v\n<green>✦ YOU WON! ✦</green>\n<yellow>%v</yellow>",
						ct.Name(),
						rewardName)

					utils.WritePacket(session, &packet.SetActorData{
						EntityRuntimeID: utils.EntityRuntimeID(session, e),
						EntityMetadata:  md,
					})
				}
			}
		}
	})
}

// updateCrateDisplayNormal resets the crate display to normal
func updateCrateDisplayNormal(pl *player.Player, ct database.CrateType) {
	u := user.GetUser(pl)
	p := server.Config.Hub.Crates[ct].Add(mgl64.Vec3{0, 1.25, 0})

	pl.H().ExecWorld(func(tx *world.Tx, _ world.Entity) {
		for e := range tx.Entities() {
			if ent, ok := e.(*entity.Ent); ok {
				if ent.H().Type() == entity.TextType && ent.Position() == p {
					// Get the crate color for the key count
					var keyColor string
					switch ct {
					case database.Common:
						keyColor = "grey"
					case database.Rare:
						keyColor = "blue"
					case database.Legendary:
						keyColor = "dark-purple"
					case database.Ancient:
						keyColor = "gold"
					case database.Vote:
						keyColor = "green"
					default:
						keyColor = "white"
					}

					session := utils.Session(pl)
					md := utils.ParseEntityMetadata(session, e)

					// Special display for Vote crate with jackpot
					if ct == database.Vote {
						jackpot := database.GetJackpot()
						md[protocol.EntityDataKeyName] = text.Colourf("%v\n<grey>You have <%v>%v</%v> keys</grey>\n<gray>Jackpot</gray> <aqua>$%s</aqua>",
							ct.Name(), keyColor, u.Data.Faction.Stats.CrateKeys[ct], keyColor, utils.AddCommas(jackpot.Amount))
					} else {
						md[protocol.EntityDataKeyName] = text.Colourf("%v\n<grey>You have <%v>%v</%v> keys</grey>",
							ct.Name(), keyColor, u.Data.Faction.Stats.CrateKeys[ct], keyColor)
					}

					utils.WritePacket(session, &packet.SetActorData{
						EntityRuntimeID: utils.EntityRuntimeID(session, e),
						EntityMetadata:  md,
					})
				}
			}
		}
	})
}

// getItemDisplayName converts raw item identifiers to user-friendly names
func getItemDisplayName(stack item.Stack) string {
	itemName, _ := stack.Item().EncodeItem()
	count := stack.Count()

	// Convert common item names to user-friendly versions
	displayName := itemName
	switch itemName {
	case "minecraft:enchanted_book":
		displayName = "Enchanted Book"
	case "minecraft:bedrock":
		displayName = "Bedrock"
	case "minecraft:obsidian":
		displayName = "Obsidian"
	case "minecraft:enchanted_golden_apple":
		displayName = "Enchanted Golden Apple"
	case "minecraft:experience_bottle":
		displayName = "Bottle o' Enchanting"
	case "minecraft:paper":
		displayName = "Bank Note"
	default:
		// Remove minecraft: prefix and replace underscores with spaces, then title case
		if strings.HasPrefix(displayName, "minecraft:") {
			displayName = strings.TrimPrefix(displayName, "minecraft:")
		}
		displayName = strings.ReplaceAll(displayName, "_", " ")
		displayName = strings.Title(displayName)
	}

	// Add count if more than 1
	if count > 1 {
		return text.Colourf("%v x%v", displayName, count)
	}
	return displayName
}
