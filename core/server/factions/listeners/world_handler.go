package listeners

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"server/server"
)

type WorldHandler struct {
	world.NopHandler
}

func (WorldHandler) HandleFireSpread(ctx *world.Context, from, to cube.Pos) {
	c1 := server.Config.Hub.BlockProtectionZone.C1
	c2 := server.Config.Hub.BlockProtectionZone.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(from.Vec3()) || box.Vec3Within(to.Vec3()) {
		ctx.Cancel()
		return
	}
}

func (WorldHandler) HandleExplosion(ctx *world.Context, position mgl64.Vec3, entities *[]world.Entity, blocks *[]cube.Pos, itemDropChance *float64, spawnFire *bool) {
	tx := ctx.Val()
	center := cube.PosFromVec3(position)

	if *itemDropChance == 0.5 {
		for x := -5.0; x <= 5.0; x++ {
			for y := -5.0; y <= 5.0; y++ {
				for z := -5.0; z <= 5.0; z++ {
					pos := center.Add(cube.PosFromVec3(mgl64.Vec3{x, y, z}))
					b := tx.Block(pos)
					if _, ok := b.(block.Bedrock); ok {
						tx.SetBlock(pos, block.Air{}, nil)
					}
				}
			}
		}
	}
}
