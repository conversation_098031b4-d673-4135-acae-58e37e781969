package listeners

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/entity/effect"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/dragonfly/server/world/particle"
	"github.com/df-mc/dragonfly/server/world/sound"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/database"
	"server/server/factions/enchants"
	"server/server/language"
	"server/server/user"
	"server/server/utils"
	"time"
)

func EnchantsHandleHurt(ctx *player.Context, _ *float64, _ *time.Duration, src world.DamageSource) {
	pl := ctx.Val()
	if s, ok := src.(entity.AttackDamageSource); ok {
		if killer, ok := s.Attacker.(*player.Player); ok {
			main, off := killer.HeldItems()
			if _, ok := main.Enchantment(enchants.FrostBite{}); ok {
				pl.AddEffect(effect.New(effect.Slowness, 1, 3*time.Second))
				pl.SendPopup(language.Translate(pl).Enchants.FrostBite)
			} else if _, ok := main.Enchantment(enchants.HomeRun{}); ok {
				if utils.RandChance(10) {
					config := block.ExplosionConfig{
						Size:      5,
						SpawnFire: true,
						Sound:     sound.Explosion{},
						Particle:  particle.HugeExplosion{},
					}
					config.Explode(pl.Tx(), pl.Position())

					pl.KnockBack(killer.Position(), 20, 15)
					pl.SetHeldItems(main.Damage(15), off)
				}
			} else if enchant, ok := main.Enchantment(enchants.ThorWrath{}); ok {
				if utils.RandChance(float64(50 * enchant.Level())) {
					e := entity.NewLightningWithDamage(world.EntitySpawnOpts{Position: pl.Position()}, 2, true, 2*time.Second)
					pl.Tx().AddEntity(e)
				}
			}
		}
	} else if s, ok := src.(entity.ProjectileDamageSource); ok {
		if killer, ok := s.Owner.(*player.Player); ok {
			main, off := killer.HeldItems()
			if _, ok := main.Enchantment(enchants.FrostShot{}); ok {
				pl.AddEffect(effect.New(effect.Slowness, 1, 3*time.Second))
				pl.SendPopup(language.Translate(pl).Enchants.FrostShot)
			} else if _, ok := main.Enchantment(enchants.SubsonicSound{}); ok {
				if utils.RandChance(30) {
					c1 := server.Config.Hub.BlockProtectionZone.C1
					c2 := server.Config.Hub.BlockProtectionZone.C2
					box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
					if box.Vec3Within(pl.Position()) {
						pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
						return
					}
					if !CheckPlayerChangeTerritory(user.GetUser(pl), cube.PosFromVec3(pl.Position())) {
						pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pl.Position()).Name))
						return
					}

					config := block.ExplosionConfig{
						Size:      5,
						SpawnFire: true,
						Sound:     sound.Explosion{},
						Particle:  particle.HugeExplosion{},
					}
					config.Explode(pl.Tx(), pl.Position())
					pl.KnockBack(killer.Position(), 15, 10)
					pl.SetHeldItems(main.Damage(main.Durability()/10), off)
					utils.SpawnParticle(pl, pl.Position(), "minecraft:sonic_explosion")
				}
			} else if _, ok := main.Enchantment(enchants.TarShot{}); ok {
				pl.SetImmobile()
				pl.SendPopup(language.Translate(pl).Enchants.TarShot)
				go func() {
					time.Sleep(time.Second)
					pl.SetMobile()
				}()
			}
		}
	}
}

func EnchantsHandleBlockBreak(ctx *player.Context, pos cube.Pos, _ *[]item.Stack, xp *int) {
	pl := ctx.Val()
	u := user.GetUser(pl)

	main, off := pl.HeldItems()
	enchant, ok := main.Enchantment(enchants.MasterKey{})
	if utils.RandChance(0.1) || ok && utils.RandChance(float64(enchant.Level())/2) {
		u := user.GetUser(pl)
		randCT := database.RandCrateType()
		u.Data.Faction.Stats.CrateKeys[randCT]++
		pl.Message(text.Colourf(language.Translate(pl).MinedKey, server.Config.Prefix, randCT.Name()))
	}
	if enchant, ok := main.Enchantment(enchants.Drill{}); ok {
		ctx.Cancel()
		pl.SetHeldItems(main.Damage(1), off)
		for xz := -1 * enchant.Level(); xz <= 1*enchant.Level(); xz++ {
			for y := -1 * enchant.Level(); y <= 1*enchant.Level(); y++ {
				var newP mgl64.Vec3
				if pl.Rotation().Direction() == cube.East || pl.Rotation().Direction() == cube.West {
					newP = pos.Vec3().Add(mgl64.Vec3{0, float64(y), float64(xz)})
				} else {
					newP = pos.Vec3().Add(mgl64.Vec3{float64(xz), float64(y), 0})
				}
				u.FactionInfo.IgnoreDrilledBlocksAt = append(u.FactionInfo.IgnoreDrilledBlocksAt, newP)
				go func() {
					time.Sleep(1 * time.Second)
					u.FactionInfo.IgnoreDrilledBlocksAt = []mgl64.Vec3{}
				}()
				pl.BreakBlock(cube.PosFromVec3(newP))
			}
		}
	} else if enchant, ok := main.Enchantment(enchants.Dynamite{}); ok {
		config := block.ExplosionConfig{
			Size:      float64(2 * enchant.Level()),
			SpawnFire: false,
			Sound:     sound.Explosion{},
			Particle:  particle.HugeExplosion{},
		}
		config.Explode(pl.Tx(), pos.Vec3())
	} else if enchant, ok := main.Enchantment(enchants.Golden{}); ok {
		u := user.GetUser(pl)
		d := 6 * enchant.Level()
		u.Data.Faction.Stats.Doubloons += float64(d)
		pl.SendPopup(text.Colourf("<green>+ <dark-grey>%v</dark-grey> doubloons</green>", d))
	} else if enchant, ok := main.Enchantment(enchants.OreXP{}); ok {
		*xp += 2 * enchant.Level()
	}
}
