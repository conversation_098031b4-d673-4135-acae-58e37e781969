package backpack

import (
	"github.com/bedrock-gophers/inv/inv"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/event"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/item/inventory"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server/database"
	"server/server/ui"
	"server/server/user"
	"server/server/utils"
)

func SendBackpackTo(pl *player.Player, size int) {
	u := user.GetUser(pl)
	sess := utils.Session(pl)

	var currentPage *int
	t := 1
	currentPage = &t

	chestInv := inventory.New(inv.ContainerChest{}.Size(), func(slot int, before, after item.Stack) {
		sess.ViewSlotChange(slot, after)
		if !(slot == 18 || slot == 26) {
			u.Data.Faction.Backpack[*currentPage][slot] = database.NewCustomStack(after)
		}
	})

	menu := inv.NewCustomMenu(text.Colourf("<emerald>PV</emerald> <yellow>[Pgs: %v]</yellow>", size), inv.ContainerChest{}, chestInv, nil)

	chestInv.Handle(ui.ChestUIHandler{
		Inventory: chestInv,
		Functions: []func(ctx *event.Context[inventory.Holder], slot int, stack item.Stack, inv *inventory.Inventory){
			func(ctx *event.Context[inventory.Holder], slot int, stack item.Stack, inv *inventory.Inventory) {
				if v, ok := stack.Value("type"); ok {
					if v, ok := v.(string); ok {
						if page, ok := stack.Value("current_page"); ok {
							if page, ok := page.(int); ok {
								if v == "backpack_back" {
									if page-1 >= 1 {
										go func() {
											pl.H().ExecWorld(func(tx *world.Tx, e world.Entity) {})
											*currentPage -= 1
											openPage(inv, u, *currentPage)
										}()
									}
									ctx.Cancel()
								} else if v == "backpack_next" {
									if page < len(u.Data.Faction.Backpack) {
										go func() {
											pl.H().ExecWorld(func(tx *world.Tx, e world.Entity) {})
											*currentPage += 1
											openPage(inv, u, *currentPage)
										}()
									}
									ctx.Cancel()
								}
							}
						}
					}
				}
			},
			nil,
			nil,
		},
	})

	openPage(chestInv, u, 1)

	inv.SendMenu(pl, menu)
}

func openPage(chestInv *inventory.Inventory, u *user.User, page int) {
	its := u.Data.Faction.Backpack[page]
	chestInv.Clear()
	for slot, cStack := range its {
		if _, ok := cStack.Stack().Item().(block.Air); ok {
			continue
		}
		utils.Panic(chestInv.SetItem(slot, cStack.Stack()))
	}
	if page == 1 {
		utils.Panic(chestInv.SetItem(18, item.NewStack(item.Arrow{}, 1).WithCustomName(text.Colourf("<red>You cannot go back further</red>")).WithValue("type", "backpack_back").WithValue("current_page", page)))
	} else {
		utils.Panic(chestInv.SetItem(18, item.NewStack(item.Arrow{}, 1).WithCustomName(text.Colourf("<dark-green>Back to page <yellow>%v</yellow></dark-green>", page-1)).WithValue("type", "backpack_back").WithValue("current_page", page)))
	}
	if page == len(u.Data.Faction.Backpack) {
		utils.Panic(chestInv.SetItem(26, item.NewStack(item.Arrow{}, 1).WithCustomName(text.Colourf("<red>You cannot go next further</red>")).WithValue("type", "backpack_next").WithValue("current_page", page)))
	} else {
		utils.Panic(chestInv.SetItem(26, item.NewStack(item.Arrow{}, 1).WithCustomName(text.Colourf("<dark-green>Next to page <yellow>%v</yellow></dark-green>", page+1)).WithValue("type", "backpack_next").WithValue("current_page", page)))
	}
}
