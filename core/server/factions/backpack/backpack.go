package backpack

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"golang.org/x/exp/maps"
	"server/server/blocks"
	"server/server/database"
	"server/server/factions/enchants"
	"server/server/user"
)

func init() {
	blocks.RegisterSpecialItem(blocks.Backpack, Backpack{})
}

type Backpack struct {
	block.EnderChest
}

func (Backpack) Stack() item.Stack {
	s := item.NewStack(block.EnderChest{}, 1).WithValue("special_item", int16(blocks.Backpack)).WithEnchantments(item.NewEnchantment(enchants.Glitter{}, 1))
	s = s.WithCustomName(text.Colourf("<emerald>Player Vault</emerald>")).WithLore("To store items in a virtual back pack")
	return s
}

func (Backpack) Use(tx *world.Tx, usr item.User, ctx *item.UseContext) bool {
	pl := usr.(*player.Player)
	u := user.GetUser(pl)
	var numOfPV int
	if u.Data.Rank() <= database.MGP {
		numOfPV = 13
	} else {
		switch u.Data.Rank() {
		case database.VIP:
			numOfPV = 5
		case database.MVP:
			numOfPV = 7
		case database.MMP:
			numOfPV = 9
		case database.MLP:
			numOfPV = 11
		default:
			numOfPV = 1
		}
	}
	size := len(maps.Keys[map[int]map[int]database.CustomStack, int, map[int]database.CustomStack](u.Data.Faction.Backpack))
	for i := 1; i <= numOfPV-size; i++ {
		if u.Data.Faction.Backpack[size+i] == nil {
			if u.Data.Faction.Backpack == nil {
				u.Data.Faction.Backpack = map[int]map[int]database.CustomStack{}
			}
			u.Data.Faction.Backpack[size+i] = make(map[int]database.CustomStack)
		}
	}

	SendBackpackTo(pl, len(maps.Keys[map[int]map[int]database.CustomStack, int, map[int]database.CustomStack](u.Data.Faction.Backpack)))
	return true
}

func (b Backpack) UseOnBlock(pos cube.Pos, face cube.Face, _ mgl64.Vec3, tx *world.Tx, usr item.User, ctx *item.UseContext) (used bool) {
	return b.Use(tx, usr, ctx)
}
