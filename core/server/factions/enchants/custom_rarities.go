package enchants

import (
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/world"
)

type CustomRarityCommon struct{}

func (CustomRarityCommon) Name() string { return "Common" }
func (CustomRarityCommon) Cost() int    { return 1 }
func (CustomRarityCommon) Weight() int  { return 0 }

type CustomRarityRare struct{}

func (CustomRarityRare) Name() string { return "Rare" }
func (CustomRarityRare) Cost() int    { return 1 }
func (CustomRarityRare) Weight() int  { return 0 }

type CustomRarityLegendary struct{}

func (CustomRarityLegendary) Name() string { return "Legendary" }
func (CustomRarityLegendary) Cost() int    { return 1 }
func (CustomRarityLegendary) Weight() int  { return 0 }

type CustomRarityAncient struct{}

func (CustomRarityAncient) Name() string { return "Ancient" }
func (CustomRarityAncient) Cost() int    { return 1 }
func (CustomRarityAncient) Weight() int  { return 0 }

type CustomRarityServerUse struct{}

func (CustomRarityServerUse) Name() string { return "" }
func (CustomRarityServerUse) Cost() int    { return 1 }
func (CustomRarityServerUse) Weight() int  { return 0 }

type Glitter struct{}

func (Glitter) Name() string {
	return ""
}

func (Glitter) MaxLevel() int {
	return 1
}

func (Glitter) Cost(int) (int, int) {
	return 0, 1
}

func (Glitter) Rarity() item.EnchantmentRarity {
	return CustomRarityServerUse{}
}

func (Glitter) CompatibleWithEnchantment(item.EnchantmentType) bool {
	return true
}

func (Glitter) CompatibleWithItem(_ world.Item) bool {
	return true
}
