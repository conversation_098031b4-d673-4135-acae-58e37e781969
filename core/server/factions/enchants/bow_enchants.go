package enchants

import (
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/world"
)

type FrostShot struct{}

func (FrostShot) Name() string {
	return "Frost Shot"
}

func (FrostShot) MaxLevel() int {
	return 1
}

func (FrostShot) Cost(int) (int, int) {
	return 20, 20
}

func (FrostShot) Rarity() item.EnchantmentRarity {
	return CustomRarityAncient{}
}

func (FrostShot) CompatibleWithEnchantment(item.EnchantmentType) bool {
	return true
}

func (FrostShot) CompatibleWithItem(i world.Item) bool {
	_, ok := i.(item.Bow)
	return ok
}

type SubsonicSound struct{}

func (SubsonicSound) Name() string {
	return "Subsonic Sound"
}

func (SubsonicSound) MaxLevel() int {
	return 1
}

func (SubsonicSound) Cost(int) (int, int) {
	return 20, 20
}

func (SubsonicSound) Rarity() item.EnchantmentRarity {
	return CustomRarityLegendary{}
}

func (SubsonicSound) CompatibleWithEnchantment(item.EnchantmentType) bool {
	return true
}

func (SubsonicSound) CompatibleWithItem(i world.Item) bool {
	_, ok := i.(item.Bow)
	return ok
}

type TarShot struct{}

func (TarShot) Name() string {
	return "Tar Shot"
}

func (TarShot) MaxLevel() int {
	return 2
}

func (TarShot) Cost(int) (int, int) {
	return 20, 20
}

func (TarShot) Rarity() item.EnchantmentRarity {
	return CustomRarityAncient{}
}

func (TarShot) CompatibleWithEnchantment(item.EnchantmentType) bool {
	return true
}

func (TarShot) CompatibleWithItem(i world.Item) bool {
	_, ok := i.(item.Bow)
	return ok
}
