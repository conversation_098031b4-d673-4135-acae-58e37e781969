package enchants

import (
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/world"
)

type Drill struct{}

func (Drill) Name() string {
	return "Drill"
}

func (Drill) MaxLevel() int {
	return 2
}

func (Drill) Cost(int) (int, int) {
	return 20, 20
}

func (Drill) Rarity() item.EnchantmentRarity {
	return CustomRarityRare{}
}

func (Drill) CompatibleWithEnchantment(item.EnchantmentType) bool {
	return true
}

func (Drill) CompatibleWithItem(i world.Item) bool {
	t, ok := i.(item.Tool)
	return ok && t.ToolType() == item.TypePickaxe
}

type Dynamite struct{}

func (Dynamite) Name() string {
	return "Dynamite"
}

func (Dynamite) MaxLevel() int {
	return 4
}

func (Dynamite) Cost(int) (int, int) {
	return 20, 20
}

func (Dynamite) Rarity() item.EnchantmentRarity {
	return CustomRarityRare{}
}

func (Dynamite) CompatibleWithEnchantment(item.EnchantmentType) bool {
	return true
}

func (Dynamite) CompatibleWithItem(i world.Item) bool {
	t, ok := i.(item.Tool)
	return ok && t.ToolType() == item.TypePickaxe
}

type Golden struct{}

func (Golden) Name() string {
	return "Golden"
}

func (Golden) MaxLevel() int {
	return 2
}

func (Golden) Cost(int) (int, int) {
	return 20, 20
}

func (Golden) Rarity() item.EnchantmentRarity {
	return CustomRarityRare{}
}

func (Golden) CompatibleWithEnchantment(item.EnchantmentType) bool {
	return true
}

func (Golden) CompatibleWithItem(i world.Item) bool {
	t, ok := i.(item.Tool)
	return ok && t.ToolType() == item.TypePickaxe
}

type MasterKey struct{}

func (MasterKey) Name() string {
	return "Master Key"
}

func (MasterKey) MaxLevel() int {
	return 2
}

func (MasterKey) Cost(int) (int, int) {
	return 20, 20
}

func (MasterKey) Rarity() item.EnchantmentRarity {
	return CustomRarityRare{}
}

func (MasterKey) CompatibleWithEnchantment(item.EnchantmentType) bool {
	return true
}

func (MasterKey) CompatibleWithItem(i world.Item) bool {
	t, ok := i.(item.Tool)
	return ok && t.ToolType() == item.TypePickaxe
}

type OreXP struct{}

func (OreXP) Name() string {
	return "Ore XP"
}

func (OreXP) MaxLevel() int {
	return 3
}

func (OreXP) Cost(int) (int, int) {
	return 20, 20
}

func (OreXP) Rarity() item.EnchantmentRarity {
	return CustomRarityRare{}
}

func (OreXP) CompatibleWithEnchantment(item.EnchantmentType) bool {
	return true
}

func (OreXP) CompatibleWithItem(i world.Item) bool {
	t, ok := i.(item.Tool)
	return ok && t.ToolType() == item.TypePickaxe
}
