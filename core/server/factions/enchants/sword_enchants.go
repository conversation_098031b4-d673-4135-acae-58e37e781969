package enchants

import (
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/world"
)

type Decapitation struct{}

func (Decapitation) Name() string {
	return "Decapitation"
}

func (Decapitation) MaxLevel() int {
	return 3
}

func (Decapitation) Cost(int) (int, int) {
	return 20, 20
}

func (Decapitation) Rarity() item.EnchantmentRarity {
	return CustomRarityCommon{}
}

func (Decapitation) CompatibleWithEnchantment(item.EnchantmentType) bool {
	return true
}

func (Decapitation) CompatibleWithItem(i world.Item) bool {
	t, ok := i.(item.Tool)
	return ok && (t.ToolType() == item.TypeSword || t.ToolType() == item.TypeAxe)
}

type FrostBite struct{}

func (FrostBite) Name() string {
	return "Frost Bite"
}

func (FrostBite) MaxLevel() int {
	return 1
}

func (FrostBite) Cost(int) (int, int) {
	return 20, 20
}

func (FrostBite) Rarity() item.EnchantmentRarity {
	return CustomRarityAncient{}
}

func (FrostBite) CompatibleWithEnchantment(item.EnchantmentType) bool {
	return true
}

func (FrostBite) CompatibleWithItem(i world.Item) bool {
	t, ok := i.(item.Tool)
	return ok && t.ToolType() == item.TypeSword
}

type HomeRun struct{}

func (HomeRun) Name() string {
	return "Home Run"
}

func (HomeRun) MaxLevel() int {
	return 1
}

func (HomeRun) Cost(int) (int, int) {
	return 20, 20
}

func (HomeRun) Rarity() item.EnchantmentRarity {
	return CustomRarityLegendary{}
}

func (HomeRun) CompatibleWithEnchantment(item.EnchantmentType) bool {
	return true
}

func (HomeRun) CompatibleWithItem(i world.Item) bool {
	t, ok := i.(item.Tool)
	return ok && t.ToolType() == item.TypeSword
}

type ThorWrath struct{}

func (ThorWrath) Name() string {
	return "Thor Wrath"
}

func (ThorWrath) MaxLevel() int {
	return 2
}

func (ThorWrath) Cost(int) (int, int) {
	return 20, 20
}

func (ThorWrath) Rarity() item.EnchantmentRarity {
	return CustomRarityAncient{}
}

func (ThorWrath) CompatibleWithEnchantment(item.EnchantmentType) bool {
	return true
}

func (ThorWrath) CompatibleWithItem(i world.Item) bool {
	t, ok := i.(item.Tool)
	return ok && t.ToolType() == item.TypeSword
}
