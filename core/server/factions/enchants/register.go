package enchants

import (
	"github.com/df-mc/dragonfly/server/item"
)

func init() {
	item.RegisterEnchantment(1000, Glitter{})

	// Sword Enchants:
	item.RegisterEnchantment(1001, Decapitation{})
	item.RegisterEnchantment(1002, <PERSON>Bite{})
	item.RegisterEnchantment(1003, HomeRun{})
	item.RegisterEnchantment(1004, <PERSON><PERSON><PERSON><PERSON>{})

	// Tool Enchants:
	item.RegisterEnchantment(1005, Drill{})
	item.RegisterEnchantment(1006, Dynamite{})
	item.RegisterEnchantment(1007, Golden{})
	item.RegisterEnchantment(1008, MasterKey{})
	item.RegisterEnchantment(1009, OreXP{})

	// Bow Enchants:
	item.RegisterEnchantment(1010, FrostShot{})
	item.RegisterEnchantment(1011, SubsonicSound{})
	item.RegisterEnchantment(1012, TarShot{})
}
