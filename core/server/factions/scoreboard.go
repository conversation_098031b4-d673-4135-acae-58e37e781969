package factions

import (
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/scoreboard"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server/database"
	"server/server/font"
	"server/server/user"
	"server/server/utils"
)

func SendMainScoreboard(pl *player.Player) {
	u := user.GetUser(pl)
	u.Scoreboard = scoreboard.New(text.Colourf("<bold><aqua>MMC Factions</aqua></bold>"))

	if u.Data.Faction.HasFaction() {
		u.Scoreboard.Set(1, "§0")
		u.Scoreboard.Set(2, text.Colourf("<grey>Faction: <green>%v</green></grey>", u.Data.Faction.Name))
		u.Scoreboard.Set(3, text.Colourf("<grey>Leader: <green>%v</green></grey>", getLeaderName(u.Data.Faction.Name)))
		u.Scoreboard.Set(4, text.Colourf("<grey>Strength: <green>%v</green></grey>", utils.ShortenNumber(u.Data.Faction.Faction().Strength, 0)))
		u.Scoreboard.Set(5, "§1")
		u.Scoreboard.Set(6, text.Colourf("<grey>Kills: <green>%v</green></grey>", u.Data.Faction.Stats.Kills))
		u.Scoreboard.Set(7, text.Colourf("<grey>Streak: <green>%v</green></grey>", u.Data.Faction.Stats.KillStreak))
		u.Scoreboard.Set(8, "§2")
		u.Scoreboard.Set(9, text.Colourf("<grey>Strength: <green>%v</green></grey>", utils.ShortenNumber(u.Data.Faction.Stats.Strength, 0)))
		u.Scoreboard.Set(10, text.Colourf("<grey>Doubloons: <green>%v</green></grey>", utils.ShortenNumber(u.Data.Faction.Stats.Doubloons, 0)))
		u.Scoreboard.Set(11, "§3")
		u.Scoreboard.Set(12, text.Colourf("<grey>Bounty: <green>%v</green></grey>", utils.ShortenNumber(u.Data.Faction.TotalBounties(), 0)))
		u.Scoreboard.Set(13, "§4")
		u.Scoreboard.Set(14, font.Transform("PLAY.MASSACREMC.NET"))
	} else {
		u.Scoreboard.Set(1, "§0")
		u.Scoreboard.Set(2, text.Colourf("<grey>Kills: <green>%v</green></grey>", u.Data.Faction.Stats.Kills))
		u.Scoreboard.Set(3, text.Colourf("<grey>Streak: <green>%v</green></grey>", u.Data.Faction.Stats.KillStreak))
		u.Scoreboard.Set(4, "§1")
		u.Scoreboard.Set(5, text.Colourf("<grey>Strength: <green>%v</green></grey>", utils.ShortenNumber(u.Data.Faction.Stats.Strength, 0)))
		u.Scoreboard.Set(6, text.Colourf("<grey>Doubloons: <green>%v</green></grey>", utils.ShortenNumber(u.Data.Faction.Stats.Doubloons, 0)))
		u.Scoreboard.Set(7, "§2")
		u.Scoreboard.Set(8, text.Colourf("<grey>Bounty: <green>%v</green></grey>", utils.ShortenNumber(u.Data.Faction.TotalBounties(), 0)))
		u.Scoreboard.Set(9, "§3")
		u.Scoreboard.Set(10, font.Transform("PLAY.MASSACREMC.NET"))
	}

	u.SendScoreboard(7)
}

func getLeaderName(factionName string) string {
	fac, err := database.DB.FindFaction(factionName)
	if err != nil || len(fac.Members) == 0 {
		return "Unknown"
	}

	leaderUUID := fac.Members[0]
	leaderUser := user.GetUserByUUID(leaderUUID)
	if leaderUser != nil {
		return leaderUser.Data.Username
	}

	leaderData, err := database.DB.FindPlayer(leaderUUID)
	if err != nil {
		return "Unknown"
	}

	return leaderData.Username
}
