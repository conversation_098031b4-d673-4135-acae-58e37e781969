package items

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/block/cube/trace"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/entity/effect"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/dragonfly/server/world/particle"
	"github.com/df-mc/dragonfly/server/world/sound"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server/blocks"
	"server/server/factions/enchants"
	"time"
)

func init() {
	blocks.RegisterSpecialItem(blocks.FakePearl, FakePearl{})
}

type FakePearl struct {
	item.EnderPearl
}

func (FakePearl) Stack() item.Stack {
	s := item.NewStack(item.EnderPearl{}, 1).WithValue("special_item", int16(blocks.FakePearl)).WithEnchantments(item.NewEnchantment(enchants.Glitter{}, 1))
	s = s.WithCustomName(text.Colourf("<emerald>Fake Enderpearl</emerald>")).WithLore("It will turn you invisible instead of teleporting you")
	return s
}

func (FakePearl) Use(tx *world.Tx, user item.User, ctx *item.UseContext) bool {
	pl := user.(*player.Player)
	conf := entity.ProjectileBehaviourConfig{
		Gravity:  0.03,
		Drag:     0.01,
		Particle: particle.EndermanTeleport{},
		Sound:    sound.Teleport{},
		Hit: func(e *entity.Ent, tx *world.Tx, target trace.Result) {
			owner, _ := e.Behaviour().(*entity.ProjectileBehaviour).Owner().Entity(tx)
			tx.PlaySound(owner.Position(), sound.Teleport{})
		},
	}
	conf.Owner = pl.H()
	pearl := world.EntitySpawnOpts{
		Position: pl.Position().Add(mgl64.Vec3{0, pl.EyeHeight(), 0}),
		Velocity: pl.Rotation().Vec3().Mul(2.5),
	}.New(entity.EnderPearlType, conf)
	pl.Tx().AddEntity(pearl)
	pl.AddEffect(effect.New(effect.Invisibility, 1, 5*time.Second))

	ctx.CountSub = 1
	return true
}

func (fp FakePearl) UseOnBlock(pos cube.Pos, face cube.Face, _ mgl64.Vec3, tx *world.Tx, usr item.User, ctx *item.UseContext) (used bool) {
	return fp.Use(tx, usr, ctx)
}
