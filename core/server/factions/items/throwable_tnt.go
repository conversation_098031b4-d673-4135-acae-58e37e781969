package items

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server/blocks"
	"server/server/factions/enchants"
	"time"
)

func init() {
	blocks.RegisterSpecialItem(blocks.ThrowableTNT, ThrowableTNT{})
}

type ThrowableTNT struct {
	block.TNT
}

func (ThrowableTNT) Stack() item.Stack {
	s := item.NewStack(ThrowableTNT{}, 1).WithValue("special_item", int16(blocks.ThrowableTNT)).WithValue("wild_only", true).WithEnchantments(item.NewEnchantment(enchants.Glitter{}, 1))
	s = s.WithCustomName(text.Colourf("<emerald>Throwable TNT</emerald>")).WithLore("Left click to throw TNT")
	return s
}

func (ThrowableTNT) Use(tx *world.Tx, user item.User, ctx *item.UseContext) bool {
	pl := user.(*player.Player)
	tx.AddEntity(entity.NewTNT(world.EntitySpawnOpts{
		Position: pl.Position().Add(mgl64.Vec3{0, pl.EyeHeight(), 0}),
		Velocity: pl.Rotation().Vec3(),
	}, 5*time.Second))

	ctx.CountSub = 1
	return true
}

func (tt ThrowableTNT) UseOnBlock(pos cube.Pos, face cube.Face, _ mgl64.Vec3, tx *world.Tx, usr item.User, ctx *item.UseContext) (used bool) {
	return tt.Use(tx, usr, ctx)
}

func (ThrowableTNT) SwingAnimation() bool {
	return true
}
