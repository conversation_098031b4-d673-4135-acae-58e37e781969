package items

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/google/uuid"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/blocks"
	"server/server/factions/enchants"
	"server/server/language"
	"server/server/user"
	"server/server/utils"
)

func init() {
	blocks.RegisterSpecialItem(blocks.PlayerHead, PlayerHead{})
}

type PlayerHead struct {
	block.Skull
	KilledUuid uuid.UUID
}

func (ph PlayerHead) Stack() item.Stack {
	s := item.NewStack(block.Skull{Type: block.PlayerHead()}, 1).WithValue("special_item", int16(blocks.PlayerHead)).WithValue("killedUuid", ph.KilledUuid.String()).WithEnchantments(item.NewEnchantment(enchants.Glitter{}, 1))
	s = s.WithCustomName(text.Colourf("<emerald><dark-grey>%v</dark-grey>'s Head</emerald>", user.FactionNameDisplay.Name(user.GetUserByUUID(ph.KilledUuid).Data))).WithLore("Click to claim 25% of their doubloons")
	return s
}

func (PlayerHead) Use(tx *world.Tx, usr item.User, ctx *item.UseContext) bool {
	pl := usr.(*player.Player)
	main, _ := pl.HeldItems()
	if d, ok := main.Value("killedUuid"); ok {
		id, _ := uuid.Parse(d.(string))
		killedUser := user.GetUserByUUID(id)
		a := 0.07 * float64(killedUser.Data.Faction.Stats.Doubloons)
		killedUser.Data.Faction.Stats.Doubloons -= a

		u := user.GetUser(pl)
		u.Data.Faction.Stats.Doubloons += a
		pl.Message(text.Colourf(language.Translate(pl).ClaimHead, server.Config.Prefix, utils.ShortenNumber(a, 0), main.CustomName()))
	}

	ctx.CountSub = 1
	return true
}

func (ph PlayerHead) UseOnBlock(pos cube.Pos, face cube.Face, _ mgl64.Vec3, tx *world.Tx, usr item.User, ctx *item.UseContext) (used bool) {
	return ph.Use(tx, usr, ctx)
}
