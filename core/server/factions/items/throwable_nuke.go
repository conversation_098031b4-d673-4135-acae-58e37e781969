package items

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/dragonfly/server/world/particle"
	"github.com/df-mc/dragonfly/server/world/sound"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"math"
	"math/rand"
	"server/server/blocks"
	"server/server/factions/enchants"
	"time"
)

func init() {
	blocks.RegisterSpecialItem(blocks.ThrowableNuke, ThrowableNuke{})
}

type ThrowableNuke struct {
	block.TNT
}

func (ThrowableNuke) Stack() item.Stack {
	s := item.NewStack(block.TNT{}, 1).WithValue("special_item", int16(blocks.ThrowableNuke)).WithValue("wild_only", true).WithEnchantments(item.NewEnchantment(enchants.Glitter{}, 1))
	s = s.WithCustomName(text.Colourf("<emerald>Throwable Nuke</emerald>")).WithLore("Left click to throw nuke")
	return s
}

func (ThrowableNuke) Use(tx *world.Tx, user item.User, ctx *item.UseContext) bool {
	pl := user.(*player.Player)
	tx.AddEntity(NewNukeTNT(world.EntitySpawnOpts{
		Position: pl.Position().Add(mgl64.Vec3{0, pl.EyeHeight(), 0}),
		Velocity: pl.Rotation().Vec3(),
	}, 5*time.Second))

	ctx.CountSub = 1
	return true
}

func (tt ThrowableNuke) UseOnBlock(pos cube.Pos, face cube.Face, _ mgl64.Vec3, tx *world.Tx, usr item.User, ctx *item.UseContext) (used bool) {
	return tt.Use(tx, usr, ctx)
}

func (ThrowableNuke) SwingAnimation() bool {
	return true
}

func NewNukeTNT(opts world.EntitySpawnOpts, fuse time.Duration) *world.EntityHandle {
	conf := tntConf
	conf.ExistenceDuration = fuse
	if opts.Velocity.Len() == 0 {
		angle := rand.Float64() * math.Pi * 2
		opts.Velocity = mgl64.Vec3{-math.Sin(angle) * 0.02, 0.1, -math.Cos(angle) * 0.02}
	}
	return opts.New(entity.TNTType, conf)
}

var tntConf = entity.PassiveBehaviourConfig{
	Gravity: 0.04,
	Drag:    0.02,
	Expire:  explodeTNT,
}

func explodeTNT(e *entity.Ent, tx *world.Tx) {
	block.ExplosionConfig{
		Size:           8,
		SpawnFire:      true,
		ItemDropChance: 0.5,
		Sound:          sound.Explosion{},
		Particle:       particle.HugeExplosion{},
	}.Explode(tx, e.Position())
}
