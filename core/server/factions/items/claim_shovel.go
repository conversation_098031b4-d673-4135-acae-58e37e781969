package items

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/blocks"
	"server/server/database"
	"server/server/factions/enchants"
	"server/server/language"
	"server/server/user"
	"server/server/utils"
)

func init() {
	blocks.RegisterSpecialItem(blocks.ClaimShovel, ClaimShovel{})
}

type ClaimShovel struct {
	item.Shovel
}

func (cs ClaimShovel) Stack() item.Stack {
	s := item.NewStack(item.Shovel{Tier: item.ToolTierGold}, 1).WithValue("special_item", int16(blocks.ClaimShovel)).WithValue("wild_only", true).WithEnchantments(item.NewEnchantment(enchants.Glitter{}, 1))
	s = s.WithCustomName(text.Colourf("<emerald>Claim Shovel</emerald>")).WithLore("Click to set first position", "Click a second time to set second position")
	return s
}

func (cs ClaimShovel) UseOnBlock(pos cube.Pos, face cube.Face, _ mgl64.Vec3, w *world.Tx, usr item.User, ctx *item.UseContext) (used bool) {
	pl := usr.(*player.Player)
	u := user.GetUser(pl)

	c1 := server.Config.Hub.BlockProtectionZone.C1
	c2 := server.Config.Hub.BlockProtectionZone.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pl.Position()) {
		pl.Message(text.Colourf(language.Translate(pl).Error.ClaimHub))
		return false
	}

	if u.Data.Faction.HasFaction() && u.Data.Faction.Role != database.Leader {
		pl.Message(text.Colourf(language.Translate(pl).Error.NotInFaction))
		return false
	}

	fac := u.Data.Faction.Faction()
	ca := database.NewClaimArea(pos.Vec3())

	var stealing bool

	facWithin := database.FactionWithin(pos.Vec3())
	if facWithin != nil && facWithin.Name != u.Data.Faction.Name {
		if facWithin.Strength >= fac.Strength {
			pl.Message(text.Colourf(language.Translate(pl).Error.CannotStealFactionClaim, facWithin.Name, facWithin.Strength, facWithin.Strength-fac.Strength))
			return false
		} else {
			facWithin.RemoveClaim(ca)
			utils.Panic(database.DB.SaveFaction(facWithin))
			stealing = true
		}
	}
	if fac.HasClaim(ca) {
		fac.RemoveClaim(ca)
		fac.RefreshChunkLines(pl)
		utils.Panic(database.DB.SaveFaction(fac))
		pl.Message(text.Colourf(language.Translate(pl).ClaimRemoved, server.Config.Prefix))
		return true
	}

	if fac.RemainingChunks() <= 0 {
		pl.Message(text.Colourf(language.Translate(pl).Error.ClaimInsufficient))
		return false
	}

	fac.ClaimArea = append(fac.ClaimArea, ca)
	fac.RefreshChunkLines(pl)
	utils.Panic(database.DB.SaveFaction(fac))

	minimum := mgl64.Vec2{float64(ca[0] << 4), float64(ca[1] << 4)}
	maximum := mgl64.Vec2{float64((ca[0]+1)<<4 - 1), float64((ca[1]+1)<<4 - 1)}
	if stealing {
		pl.Message(text.Colourf(language.Translate(pl).ClaimStealSuccess, server.Config.Prefix, facWithin.Name, minimum, maximum))
	} else {
		pl.Message(text.Colourf(language.Translate(pl).ClaimSuccess, server.Config.Prefix, minimum, maximum, fac.RemainingChunks()))
	}
	return true
}
