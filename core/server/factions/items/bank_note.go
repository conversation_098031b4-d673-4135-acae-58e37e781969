package items

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/google/uuid"
	"github.com/sandertv/gophertunnel/minecraft/text"
	core "server/server"
	"server/server/blocks"
	"server/server/database"
	"server/server/factions/enchants"
	"server/server/language"
	"server/server/user"
	"server/server/utils"
	"sync"
	"time"
)

var (
	usedBankNoteIDs = make(map[string]bool)
	usedIDsMutex    = sync.RWMutex{}
)

func init() {
	blocks.RegisterSpecialItem(blocks.BankNote, BankNote{})

	go func() {
		time.Sleep(5 * time.Second) 
		loadUsedBankNoteIDs()
	}()
}

func loadUsedBankNoteIDs() {
	if database.DB == nil {
		return
	}

	client := database.DB.Client()
	if client == nil {
		return
	}

	ctx := context.Background()
	collection := client.Database("mmc").Collection("bank_note_transactions")
	if collection == nil {
		return
	}


	cursor, err := collection.Find(ctx, map[string]interface{}{"action": "used"})
	if err != nil {
		return
	}
	defer cursor.Close(ctx)

	usedIDsMutex.Lock()
	defer usedIDsMutex.Unlock()

	for cursor.Next(ctx) {
		var result map[string]interface{}
		if err := cursor.Decode(&result); err != nil {
			continue
		}
		if uniqueID, ok := result["unique_id"].(string); ok {
			usedBankNoteIDs[uniqueID] = true
		}
	}
}


func isAlreadyUsed(uniqueID string) bool {
	usedIDsMutex.RLock()
	defer usedIDsMutex.RUnlock()
	return usedBankNoteIDs[uniqueID]
}


func markAsUsed(uniqueID string) {
	usedIDsMutex.Lock()
	defer usedIDsMutex.Unlock()
	usedBankNoteIDs[uniqueID] = true
}


func validateBankNoteAmount(amount float64) bool {

	if amount <= 0 {
		return false
	}
	if amount > ********** {
		return false
	}
	if amount != amount { 
		return false
	}
	return true
}


func generateUniqueID() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {

		return hex.EncodeToString([]byte(time.Now().Format("**************.000000")))
	}
	return hex.EncodeToString(bytes)
}


func logBankNoteTransaction(action string, uniqueID string, amount float64, playerUUID *uuid.UUID, creatorInfo string) {
	go func() {
		if database.DB == nil {
			return
		}

		client := database.DB.Client()
		if client == nil {
			return
		}

		logEntry := map[string]interface{}{
			"action":       action,
			"unique_id":    uniqueID,
			"amount":       amount,
			"timestamp":    time.Now(),
			"creator_info": creatorInfo,
		}

		if playerUUID != nil {
			logEntry["player_uuid"] = playerUUID.String()
		}

		ctx := context.Background()
		collection := client.Database("mmc").Collection("bank_note_transactions")
		if collection != nil {
			if _, err := collection.InsertOne(ctx, logEntry); err != nil {
				_ = err
			}
		}
	}()
}

type BankNote struct {
	item.Paper
	Amount float64
}

func (bn BankNote) Stack() item.Stack {
	if !validateBankNoteAmount(bn.Amount) {
		return item.Stack{}
	}

	uniqueID := generateUniqueID()
	creationTime := time.Now().Unix()

	s := item.NewStack(item.Paper{}, 1).WithValue("special_item", int16(blocks.BankNote)).WithValue("amount", bn.Amount).WithEnchantments(item.NewEnchantment(enchants.Glitter{}, 1))

	s = s.WithValue("unique_id", uniqueID)
	s = s.WithValue("creation_time", creationTime)
	s = s.WithValue("tracked", true)

	s = s.WithCustomName(text.Colourf("<bold><green>Bank Note</green></bold>")).WithLore(
		text.Colourf("<grey>Value: <gold>%v</gold> <yellow>Doubloons</yellow></grey>", utils.AddCommas(bn.Amount)),
		"",
		text.Colourf("<grey>Left click to claim your doubloons!</grey>"),
	)

	logBankNoteTransaction("created", uniqueID, bn.Amount, nil, "system_generated")

	return s
}

func (BankNote) Use(tx *world.Tx, usr item.User, ctx *item.UseContext) bool {
	pl := usr.(*player.Player)
	main, _ := pl.HeldItems()

	if !validateBankNote(main) {
		pl.Message(text.Colourf("<red>This bank note appears to be invalid or corrupted!</red>"))
		ctx.CountSub = 1 
		return true
	}

	if amount, ok := main.Value("amount"); ok {
		a := amount.(float64)

		uniqueID, hasUniqueID := main.Value("unique_id")
		if !hasUniqueID {
			pl.Message(text.Colourf("<red>This bank note is missing tracking data!</red>"))
			ctx.CountSub = 1
			return true
		}

		u := user.GetUser(pl)
		u.Data.Faction.Stats.Doubloons += a
		pl.Message(text.Colourf(language.Translate(pl).ObtainedBankNote, core.Config.Prefix, utils.AddCommas(a)))

		markAsUsed(uniqueID.(string))

		playerUUID := pl.UUID()
		logBankNoteTransaction("used", uniqueID.(string), a, &playerUUID, pl.Name())

		// CRITICAL: Save player data immediately after bank note usage to prevent loss
		user.SavePlayerDataImmediate(pl)
	}

	ctx.CountSub = 1
	return true
}

func validateBankNote(stack item.Stack) bool {
	if tracked, ok := stack.Value("tracked"); !ok || !tracked.(bool) {
		return false
	}

	uniqueID, hasUniqueID := stack.Value("unique_id")
	if !hasUniqueID {
		return false
	}

	if isAlreadyUsed(uniqueID.(string)) {
		return false
	}

	if creationTime, ok := stack.Value("creation_time"); !ok {
		return false
	} else {
		now := time.Now().Unix()
		created := creationTime.(int64)
		if created > now || created < (now-86400*365) { 
			return false
		}
	}

	if amount, ok := stack.Value("amount"); !ok {
		return false
	} else {
		amt := amount.(float64)
		if !validateBankNoteAmount(amt) {
			return false
		}
	}

	return true
}

func (bn BankNote) UseOnBlock(pos cube.Pos, face cube.Face, _ mgl64.Vec3, tx *world.Tx, usr item.User, ctx *item.UseContext) (used bool) {
	return bn.Use(tx, usr, ctx)
}
