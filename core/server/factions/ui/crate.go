package ui

import (
	"github.com/bedrock-gophers/inv/inv"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/event"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/item/inventory"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/dragonfly/server/world/particle"
	"github.com/df-mc/dragonfly/server/world/sound"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"github.com/go-gl/mathgl/mgl64"
	"image/color"
	"math"
	"math/rand"
	core "server/server"
	items2 "server/server/blocks"
	"server/server/database"
	"server/server/factions/items"
	"server/server/language"
	"server/server/ui"
	"server/server/user"
	"server/server/utils"
	"time"
)

var enchantmentTypes []item.EnchantmentType

func init() {
	enchantmentTypes = utils.Filter[item.EnchantmentType](item.Enchantments(), func(et item.EnchantmentType) bool {
		return et.Name() != ""
	})
}

func SendCrateTo(pl *player.Player, ct *database.CrateType) {
	u := user.GetUser(pl)
	sess := utils.Session(pl)

	chestInv := inventory.New(inv.ContainerChest{}.Size(), func(slot int, before, after item.Stack) {
		sess.ViewSlotChange(slot, after)
	})
	menu := inv.NewCustomMenu(text.Colourf("<dark-grey>%v</dark-grey> <green>Crates</green>", ct.Name()), inv.ContainerChest{}, chestInv, nil)
	var isRolling bool
	chestInv.Handle(ui.ChestUIHandler{
		Inventory: chestInv,
		Functions: []func(ctx *event.Context[inventory.Holder], slot int, stack item.Stack, inv *inventory.Inventory){
			func(ctx *event.Context[inventory.Holder], slot int, stack item.Stack, invMenu *inventory.Inventory) {
				ctx.Cancel()
				if v, ok := stack.Value("rolls"); ok && !isRolling {
					rolls := v.(int)
					slots := map[int][]int{
						1: {13},
						5: {11, 12, 13, 14, 15},
						9: {9, 10, 11, 12, 13, 14, 15, 16, 17},
					}[rolls]
					if len(pl.Inventory().Items())+rolls > 36 {
						pl.Message(text.Colourf(language.Translate(pl).Error.InventoryFull))
					} else if u.Data.Faction.Stats.CrateKeys[*ct] >= rolls {
						u.Data.Faction.Stats.CrateKeys[*ct] -= rolls
						isRolling = true
						go func() {
							for t := 0; t < 20; t++ {
								for _, slot := range slots {
									_ = invMenu.SetItem(slot, Roll(*ct))
								}
								pl.PlaySound(sound.Click{})

								if t < 13 {
									time.Sleep(50 * time.Millisecond)
								} else if t < 17 {
									time.Sleep(250 * time.Millisecond)
								} else {
									time.Sleep(750 * time.Millisecond)
								}
							}

							pl.H().ExecWorld(func(tx *world.Tx, e world.Entity) {
								for _, slot := range slots {
									s, _ := invMenu.Item(slot)

									// Check for jackpot win on Vote crates
									if *ct == database.Vote {
										handleVoteCrateReward(pl, s, tx)
									}

									if _, err := pl.Inventory().AddItem(s); err != nil {
										pl.Drop(s)
										pl.Message(text.Colourf(language.Translate(pl).Error.InventoryFull))
									}
								}
								isRolling = false
							})
						}()
					}
				}
			},
			nil,
			func(ctx *event.Context[inventory.Holder], slot int, stack item.Stack, inv *inventory.Inventory) {
				ctx.Cancel()
			},
		},
	})

	empty := item.Stack{}
	redG := item.NewStack(block.StainedGlass{Colour: item.ColourRed()}, 1)
	greenG := item.NewStack(block.StainedGlass{Colour: item.ColourGreen()}, 1)
	copper := item.NewStack(block.RawCopper{}, 1)
	cks := u.Data.Faction.Stats.CrateKeys
	infoSign := item.NewStack(block.Sign{Wood: block.OakWood()}, 1).WithCustomName(text.Colourf("<gold>Crates Info</gold>")).WithLore(text.Colourf(
		"<green>Vote: <dark-grey>%v</dark-grey></green>\n"+
			"<green>Common: <dark-grey>%v</dark-grey></green>\n"+
			"<green>Rare: <dark-grey>%v</dark-grey></green>\n"+
			"<green>Legendary: <dark-grey>%v</dark-grey></green>\n"+
			"<green>Ancient: <dark-grey>%v</dark-grey></green>\n",
		cks[database.Vote],
		cks[database.Common],
		cks[database.Rare],
		cks[database.Legendary],
		cks[database.Ancient],
	))

	for slot, it := range []item.Stack{
		greenG, redG, greenG, redG, greenG, redG, greenG, redG, greenG,
		empty, empty, empty, empty, empty, empty, empty, empty, empty,
		infoSign, copper, copper, copper, item.NewStack(block.Sign{Wood: block.OakWood()}, 1).WithCustomName(text.Colourf("<gold>Roll 9</gold>")).WithValue("rolls", 9), copper, item.NewStack(block.Sign{Wood: block.OakWood()}, 1).WithCustomName(text.Colourf("<gold>Roll 5</gold>")).WithValue("rolls", 5), copper, item.NewStack(block.Sign{Wood: block.OakWood()}, 1).WithCustomName(text.Colourf("<gold>Roll 1</gold>")).WithValue("rolls", 1),
	} {
		_ = chestInv.SetItem(slot, it)
	}

	for slot := 9; slot < 18; slot++ {
		_ = chestInv.SetItem(slot, Roll(*ct))
	}

	inv.SendMenu(pl, menu)
}

func Roll(ct database.CrateType) item.Stack {
	if utils.RandChance(40) {
		return items2.AddEnchantmentLore(item.NewStack(item.EnchantedBook{}, 1).WithEnchantments(item.NewEnchantment(enchantmentTypes[rand.Intn(len(enchantmentTypes))], 1)))
	} else {
		chooseKit := func(chance float64) database.Rank {
			for _, ct := range []database.Rank{database.MMP, database.MVP, database.VIP} {
				if utils.RandChance(chance) {
					return ct
				}
			}
			return database.Player
		}

		xp := item.NewStack(item.BottleOfEnchanting{}, (1+rand.Intn(2))*6)

		its := map[database.CrateType][]item.Stack{
			database.Common: {
				item.NewStack(block.Bedrock{}, 5),
				item.NewStack(block.Obsidian{}, 10),
				item.NewStack(item.EnchantedApple{}, 10),
				xp,
				items.Kit{Type: chooseKit(20)}.Stack(),
				items.BankNote{Amount: float64(8000 + rand.Intn(7001))}.Stack(),
			},
			database.Rare: {
				item.NewStack(block.Bedrock{}, 15),
				item.NewStack(block.Obsidian{}, 20),
				item.NewStack(item.EnchantedApple{}, 20),
				xp,
				items.Kit{Type: chooseKit(40)}.Stack(),
				items.BankNote{Amount: float64(17000 + rand.Intn(8001))}.Stack(),
			},
			database.Legendary: {
				item.NewStack(block.Bedrock{}, 35),
				item.NewStack(block.Obsidian{}, 30),
				item.NewStack(item.EnchantedApple{}, 30),
				xp,
				items.Kit{Type: chooseKit(60)}.Stack(),
				items.BankNote{Amount: float64(26000 + rand.Intn(9001))}.Stack(),
			},
			database.Ancient: {
				item.NewStack(block.Bedrock{}, 50),
				item.NewStack(block.Obsidian{}, 40),
				item.NewStack(item.EnchantedApple{}, 40),
				xp,
				items.Kit{Type: chooseKit(80)}.Stack(),
				items.BankNote{Amount: float64(50000 + rand.Intn(50001))}.Stack(),
			},
			database.Vote: {
				item.NewStack(block.Bedrock{}, 3),
				item.NewStack(block.Obsidian{}, 5),
				item.NewStack(item.EnchantedApple{}, 5),
				xp,
				items.Kit{Type: chooseKit(15)}.Stack(),
				items.BankNote{Amount: float64(5000 + rand.Intn(5001))}.Stack(), // 5K-10K doubloons
			},
		}
		return its[ct][rand.Intn(len(its[ct]))]
	}
}

// handleVoteCrateReward handles vote crate rewards including jackpot logic
func handleVoteCrateReward(pl *player.Player, reward item.Stack, tx *world.Tx) {
	// Add random amount to jackpot (1K-10K doubloons)
	jackpotIncrease := float64(1000 + rand.Intn(9001))
	database.AddToJackpot(jackpotIncrease)

	// Check for jackpot win (0.01% chance = 1 in 10,000)
	if rand.Intn(10000) == 0 {
		// JACKPOT WIN!
		wonAmount := database.WinJackpot(pl.Name())

		// Give jackpot doubloons to player
		u := user.GetUser(pl)
		u.Data.Faction.Stats.Doubloons += wonAmount

		// Immediate save for jackpot win
		user.SavePlayerDataImmediate(pl)

		// Announce to all players
		announceJackpotWin(pl, wonAmount, tx)

		// Spawn fireworks
		spawnJackpotFireworks(pl, tx)

		// Special jackpot message to winner
		pl.Message(text.Colourf("<gold><bold>🎉 JACKPOT! 🎉</bold></gold>\n<yellow>You won %s doubloons!</yellow>", utils.AddCommas(wonAmount)))
	}
}

// announceJackpotWin announces the jackpot win to all players
func announceJackpotWin(winner *player.Player, amount float64, tx *world.Tx) {
	announcement := text.Colourf(
		"<gold><bold>🎉 JACKPOT WINNER! 🎉</bold></gold>\n" +
		"<yellow>%s</yellow> <green>won</green> <gold>%s doubloons</gold> <green>from the Vote Jackpot!</green>",
		winner.Name(), utils.AddCommas(amount))

	// Send to all online players
	for pl := range core.MCServer.Players(tx) {
		pl.Message(announcement)
		pl.PlaySound(sound.LevelUp{})
	}
}

// spawnJackpotFireworks spawns colorful fireworks around the winner
func spawnJackpotFireworks(pl *player.Player, tx *world.Tx) {
	pos := pl.Position()

	// Beautiful firework colors
	colors := []color.RGBA{
		{R: 255, G: 0, B: 0, A: 255},     // Red
		{R: 255, G: 165, B: 0, A: 255},   // Orange
		{R: 255, G: 255, B: 0, A: 255},   // Yellow
		{R: 0, G: 255, B: 0, A: 255},     // Green
		{R: 0, G: 191, B: 255, A: 255},   // Blue
		{R: 138, G: 43, B: 226, A: 255},  // Purple
		{R: 255, G: 20, B: 147, A: 255},  // Pink
		{R: 255, G: 215, B: 0, A: 255},   // Gold
	}

	// Spawn multiple colorful fireworks in a circle around the player
	for i := 0; i < 16; i++ {
		angle := float64(i) * 0.392699 // 22.5 degrees in radians (16 fireworks)
		distance := 2.0 + rand.Float64()*3.0 // Random distance 2-5 blocks
		x := pos.X() + distance * math.Cos(angle)
		z := pos.Z() + distance * math.Sin(angle)
		y := pos.Y() + 1.0 + rand.Float64()*4.0 // Random height 1-5 blocks up

		fireworkPos := mgl64.Vec3{x, y, z}
		selectedColor := colors[i%len(colors)]

		// Spawn colorful dust particles (main firework effect)
		tx.AddParticle(fireworkPos, particle.Dust{Colour: selectedColor})
		tx.AddParticle(fireworkPos.Add(mgl64.Vec3{0.5, 0, 0}), particle.Dust{Colour: selectedColor})
		tx.AddParticle(fireworkPos.Add(mgl64.Vec3{-0.5, 0, 0}), particle.Dust{Colour: selectedColor})
		tx.AddParticle(fireworkPos.Add(mgl64.Vec3{0, 0, 0.5}), particle.Dust{Colour: selectedColor})
		tx.AddParticle(fireworkPos.Add(mgl64.Vec3{0, 0, -0.5}), particle.Dust{Colour: selectedColor})

		// Add explosion effects for some fireworks
		if i%4 == 0 {
			tx.AddParticle(fireworkPos, particle.HugeExplosion{})
		}
	}

	// Play multiple explosion sounds with slight delays for realistic effect
	go func() {
		for i := 0; i < 5; i++ {
			pl.PlaySound(sound.Explosion{})
			time.Sleep(200 * time.Millisecond)
		}
	}()
}
