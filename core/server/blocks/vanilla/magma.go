package vanilla

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/block/model"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/world"
)

type Magma struct {
}

func (Magma) LightDiffusionLevel() uint8 {
	return 2
}

func (Magma) LightEmissionLevel() uint8 {
	return 3
}

// EntityInside ...
func (m Magma) EntityInside(_ cube.Pos, _ *world.World, e world.Entity) {
	e.(entity.Living).Hurt(1, MagmaDamageSource{})
}

func (Magma) EncodeItem() (name string, meta int16) {
	return "minecraft:magma", 0
}

func (Magma) EncodeBlock() (string, map[string]any) {
	return "minecraft:magma", nil
}

var hashMagma = block.NextHash()

func (Magma) Hash() (uint64, uint64) {
	return hashMagma, 0
}

// Model ...
func (Magma) Model() world.BlockModel {
	return model.Solid{}
}

// MagmaDamageSource is used for damage caused by being on Magma.
type MagmaDamageSource struct{}

func (MagmaDamageSource) ReducedByResistance() bool { return true }

func (MagmaDamageSource) ReducedByArmour() bool { return true }
func (MagmaDamageSource) Fire() bool            { return true }
func (s MagmaDamageSource) IgnoreTotem() bool {
	return false
}
