package vanilla

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/model"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/item/enchantment"
	"github.com/df-mc/dragonfly/server/world"
)

type Ice struct {
}

// BreakInfo ...
func (i Ice) BreakInfo() block.BreakInfo {
	return block.BreakInfo{
		Hardness:        5,
		BlastResistance: 5 * 5,
		Harvestable: func(t item.Tool) bool {
			return true
		},
		Effective: func(t item.Tool) bool {
			return t.ToolType() == item.TypePickaxe
		},
		Drops: func(t item.Tool, enchantments []item.Enchantment) []item.Stack {
			if hasSilkTouch(enchantments) {
				return []item.Stack{item.NewStack(i, 1)}
			}
			return nil
		},
	}
}

// LightDiffusionLevel always returns 2.
func (Ice) LightDiffusionLevel() uint8 {
	return 2
}

// LightEmissionLevel returns 2.
func (Ice) LightEmissionLevel() uint8 {
	return 2
}

// Friction ...
func (i Ice) Friction() float64 {
	return 0.98
}

// EncodeItem ...
func (Ice) EncodeItem() (name string, meta int16) {
	return "minecraft:ice", 0
}

// EncodeBlock ...
func (Ice) EncodeBlock() (string, map[string]any) {
	return "minecraft:ice", nil
}

var hashIce = block.NextHash()

func (i Ice) Hash() (uint64, uint64) {
	return hashIce, 0
}

func (i Ice) Model() world.BlockModel {
	return model.Solid{}
}

func hasSilkTouch(enchantments []item.Enchantment) bool {
	for _, enchant := range enchantments {
		if enchant.Type() == enchantment.SilkTouch {
			return true
		}
	}
	return false
}
