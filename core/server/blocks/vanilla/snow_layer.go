package vanilla

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"math/rand"
	"server/server/utils"
)

// SnowLayer is a ground cover block found on the surface in snowy biomes, and
// can be replenished during snowfall.
type SnowLayer struct {
	// Height is the height of the snow layer. It ranges from 0 to 7.
	Height int
	// Covered specifies if the snow layer is covered by another block.
	Covered bool
}

// UseOnBlock ...
func (s SnowLayer) UseOnBlock(pos cube.Pos, face cube.Face, _ mgl64.Vec3, tx *world.Tx, user item.User, ctx *item.UseContext) (used bool) {
	bottomBlock := tx.Block(pos.Side(cube.FaceDown))
	if _, ok := bottomBlock.(block.Grass); ok {
		s.Covered = true
	}

	clickedBlock := tx.Block(pos)
	if clickedSnowLayer, ok := clickedBlock.(SnowLayer); ok {
		if clickedSnowLayer.Height < 7 {
			s.Height = clickedSnowLayer.Height + 1
			if s.Height == 7 {
				utils.Place(tx, pos, block.Snow{}, user, ctx)
			} else {
				utils.Place(tx, pos, s, user, ctx)
			}
			return ctx.CountSub > 0
		}
	}

	pos, _, used = utils.FirstReplaceable(tx, pos, face, s)
	if !used {
		return
	}

	utils.Place(tx, pos, s, user, ctx)
	return ctx.CountSub > 0
}

// BreakInfo ...
func (s SnowLayer) BreakInfo() block.BreakInfo {
	return block.BreakInfo{
		Hardness:        0.2,
		BlastResistance: 5 * 5,
		Harvestable: func(t item.Tool) bool {
			return true
		},
		Effective: func(t item.Tool) bool {
			return t.ToolType() == item.TypeShovel
		},
		Drops: func(t item.Tool, enchantments []item.Enchantment) []item.Stack {
			if hasSilkTouch(enchantments) {
				return []item.Stack{item.NewStack(s, 1)}
			}
			return []item.Stack{item.NewStack(item.Snowball{}, 4)}
		},
	}
}

// ScheduledTick ...
func (s SnowLayer) ScheduledTick(pos cube.Pos, tx *world.Tx, _ *rand.Rand) {
	s.tick(pos, tx)
}

// RandomTick ...
func (s SnowLayer) RandomTick(pos cube.Pos, tx *world.Tx, _ *rand.Rand) {
	s.tick(pos, tx)
}

// tick ...
func (s SnowLayer) tick(pos cube.Pos, tx *world.Tx) {
	bottomBlock := tx.Block(pos.Side(cube.FaceDown))
	if _, ok := bottomBlock.(block.Grass); ok {
		s.Covered = true
		tx.SetBlock(pos, s, nil)
	}

	if tx.Light(pos) >= 12 {
		newHeight := s.Height - 1
		if newHeight < 0 {
			tx.SetBlock(pos, block.Air{}, nil)
		} else {
			s.Height = newHeight
			tx.SetBlock(pos, s, nil)
		}
	}
}

// AllSnowLayers ...
func AllSnowLayers() (b []world.Block) {
	for i := 0; i <= 7; i++ {
		b = append(b, SnowLayer{Height: i, Covered: false})
		b = append(b, SnowLayer{Height: i, Covered: true})
	}
	return
}

// EncodeItem ...
func (s SnowLayer) EncodeItem() (name string, meta int16) {
	return "minecraft:snow_layer", 0
}

// EncodeBlock ...
func (s SnowLayer) EncodeBlock() (string, map[string]any) {
	return "minecraft:snow_layer", map[string]any{"covered_bit": s.Covered, "height": int32(s.Height)}
}

var hashSnowLayer = block.NextHash()

func (s SnowLayer) Hash() (uint64, uint64) {
	var b uint8
	if s.Covered {
		b = 0
	} else {
		b = 1
	}
	return hashSnowLayer, uint64(s.Height<<8) | uint64(b<<2)
}

func (s SnowLayer) Model() world.BlockModel {
	return SnowLayerModel{Height: s.Height}
}

type SnowLayerModel struct {
	// Height is the height of the snow layer. It ranges from 0 to 7.
	Height int
}

// BBox ...
func (s SnowLayerModel) BBox(cube.Pos, world.BlockSource) []cube.BBox {
	height := 0.5
	if s.Height >= 3 {
		height = 1
	}
	return []cube.BBox{cube.Box(0, 0, 0, 1, height, 1)}
}

// FaceSolid ...
func (s SnowLayerModel) FaceSolid(cube.Pos, cube.Face, world.BlockSource) bool {
	return s.Height >= 7
}
