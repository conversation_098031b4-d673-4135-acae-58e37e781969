package vanilla

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/block/model"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
)

// Slime is a storage block equivalent to nine slimeballs. It has both sticky and bouncy properties making it useful in
// conjunction with pistons to move both blocks and entities.
type Slime struct {
}

// BreakInfo ...
func (s Slime) BreakInfo() block.BreakInfo {
	return block.BreakInfo{
		Harvestable: func(t item.Tool) bool {
			return true
		},
		Effective: func(t item.Tool) bool {
			return false
		},
		Drops: func(item.Tool, []item.Enchantment) []item.Stack {
			var stack []item.Stack
			for _, it := range []world.Item{s} {
				stack = append(stack, item.NewStack(it, 1))
			}
			return stack
		},
	}
}

// Friction ...
func (Slime) Friction() float64 {
	return 0.8
}

// EncodeItem ...
func (Slime) EncodeItem() (name string, meta int16) {
	return "minecraft:slime", 0
}

// EncodeBlock ...
func (Slime) EncodeBlock() (name string, properties map[string]any) {
	return "minecraft:slime", nil
}

var hashSlime = block.NextHash()

// Hash ...
func (Slime) Hash() (uint64, uint64) {
	return hashSlime, 0
}

// Model ...
func (Slime) Model() world.BlockModel {
	return model.Solid{}
}

// EntityLand ...
func (Slime) EntityLand(_ cube.Pos, _ *world.World, e world.Entity, distance *float64) {
	if s, ok := e.(interface {
		Sneaking() bool
	}); !ok || !s.Sneaking() {
		*distance = 0
	}
	if v, ok := e.(interface {
		Velocity() mgl64.Vec3
		SetVelocity(mgl64.Vec3)
	}); ok {
		vel := v.Velocity()
		vel[1] = -vel[1]
		v.SetVelocity(vel)
	}
}
