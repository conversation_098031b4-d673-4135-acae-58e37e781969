package polar_bear

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/block/customblock"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/item/category"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"image"
	"os"
	"path"
	"server/server/blocks"
	"server/server/utils"
)

type Head struct {
	Facing cube.Direction
}

func (h Head) Name() string {
	return "Polar Bear Head"
}

func (h Head) Category() category.Category {
	return category.Construction()
}

func (h Head) Texture() image.Image {
	return blocks.Texture(path.Join(".", "server", "blocks", "heads", "polar_bear", "head.png"))
}

func (h Head) Geometry() []byte {
	data, err := os.ReadFile(path.Join(".", "server", "blocks", "heads", "polar_bear", "head.geo.json"))
	if err != nil {
		panic(err)
	}
	return data
}

func (h Head) Textures() map[string]image.Image {
	return map[string]image.Image{
		"polar_bear_head": h.Texture(),
	}
}

func (h Head) Properties() customblock.Properties {
	return customblock.Properties{
		CollisionBox: cube.Box(0, 0, 0, 1, 1, 1),
		SelectionBox: cube.Box(0, 0, 0, 1, 1, 1),
		Geometry:     "geometry.heads.polar_bear",
		Textures: map[string]customblock.Material{
			"*": customblock.NewMaterial("polar_bear_head", customblock.OpaqueRenderMethod()),
		},
		Rotation: cube.Pos{0, 0, 0},
	}
}

func (h Head) EncodeItem() (name string, meta int16) {
	return "mmc:polar_bear_head", 0
}

func (h Head) EncodeBlock() (string, map[string]any) {
	return "mmc:polar_bear_head", map[string]any{"rotation": int32(h.Facing)}
}

var nextHash = block.NextHash()

func (h Head) Hash() (uint64, uint64) {
	return nextHash, uint64(h.Facing) << 8
}

func (h Head) Model() world.BlockModel {
	return Model{}
}

func (h Head) States() map[string][]any {
	return map[string][]any{
		"rotation": {int32(0), int32(1), int32(2), int32(3)},
	}
}

func (h Head) Permutations() []customblock.Permutation {
	return []customblock.Permutation{
		{
			Condition: "query.block_state('rotation') == 1",
			Properties: customblock.Properties{
				Rotation: cube.Pos{0, 3, 0},
			},
		},
		{
			Condition: "query.block_state('rotation') == 2",
			Properties: customblock.Properties{
				Rotation: cube.Pos{0, 2, 0},
			},
		},
		{
			Condition: "query.block_state('rotation') == 3",
			Properties: customblock.Properties{
				Rotation: cube.Pos{0, 1, 0},
			},
		},
	}
}

func (h Head) UseOnBlock(pos cube.Pos, face cube.Face, _ mgl64.Vec3, w *world.Tx, user item.User, ctx *item.UseContext) (used bool) {
	pos, _, used = utils.FirstReplaceable(w, pos, face, h)
	if !used {
		return
	}
	h.Facing = user.Rotation().Direction()
	utils.Place(w, pos, h, user, ctx)
	return used
}

type Model struct{}

func (Model) BBox(cube.Pos, world.BlockSource) []cube.BBox {
	return []cube.BBox{cube.Box(0.375, 0, 0.3437, 0.625, 0.375, 0.6562)}
}

func (Model) FaceSolid(cube.Pos, cube.Face, world.BlockSource) bool {
	return false
}
