package language

import (
	"github.com/df-mc/dragonfly/server/player"
	"path"
	"server/server"
	"server/server/utils"
)

func RegisterLanguages() {
	for lang, codes := range server.Config.Languages {
		if err := registerLanguage(lang); err != nil {
			panic(err)
		}
		for _, code := range codes {
			fileCodes[code] = lang
		}
	}
}

func Translate(pl *player.Player) Config {
	return languages[fileCodes[pl.Locale().String()]]
}

var languages = map[string]Config{}

var fileCodes = make(map[string]string)

func registerLanguage(langFile string) error {
	lang, err := utils.ReadConfig[Config](path.Join(".", "config", "languages", langFile))
	if err != nil {
		return err
	}
	languages[langFile] = lang
	return nil
}
