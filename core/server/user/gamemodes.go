package user

type FlightGameMode struct{}

func (fg FlightGameMode) AllowsEditing() bool {
	return true
}

func (fg FlightGameMode) AllowsTakingDamage() bool {
	return true
}

func (fg FlightGameMode) CreativeInventory() bool {
	return false
}

func (fg FlightGameMode) HasCollision() bool {
	return true
}

func (fg FlightGameMode) AllowsFlying() bool {
	return true
}

func (fg FlightGameMode) AllowsInteraction() bool {
	return true
}

func (fg FlightGameMode) Visible() bool {
	return true
}
