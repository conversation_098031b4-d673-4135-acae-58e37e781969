package user

import (
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server/database"
)

type nameConfig struct {
	ShowRank    bool
	ShowFaction bool
}

func (nc nameConfig) Name(pd *database.PlayerData) string {
	var facStr string
	if nc.ShowFaction && pd.Faction.HasFaction() {
		facStr = text.Colourf("<dark-aqua>%v</dark-aqua> ", pd.Faction.Name)
	}

	if nc.ShowRank {
		return text.Colourf("%v%v<grey>%v</grey>", facStr, pd.Rank().Prefix(), pd.Username)
	}

	return text.Colourf("<grey>%v</grey>", pd.Username)
}

// FactionMemberName returns a display name with faction role and username (no faction name or rank)
func FactionMemberName(pd *database.PlayerData) string {
	if pd.Faction.HasFaction() {
		rolePrefix := text.Colourf(database.RolePrefixes[pd.Faction.Role])
		return text.Colourf("%v<aqua>%v</aqua>", rolePrefix, pd.Username)
	}
	return text.Colourf("<aqua>%v</aqua>", pd.Username)
}

var FactionNameDisplay = nameConfig{ShowRank: true, ShowFaction: true}
