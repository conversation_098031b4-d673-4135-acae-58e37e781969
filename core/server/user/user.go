package user

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/scoreboard"
	"github.com/df-mc/dragonfly/server/player/title"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/golang-collections/collections/stack"
	"github.com/google/uuid"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"log/slog"
	"math"
	"server/server/cooldown"
	"server/server/database"
	"server/server/font"
	"server/server/language"
	server "server/server"
	"server/server/utils"
	"strings"
	"sync"
	"time"
)

// Debounced save system to prevent performance issues
var (
	pendingSaves = make(map[uuid.UUID]*time.Timer)
	savesMutex   = sync.RWMutex{}
)

var (
	userMu sync.Mutex
	users  = map[uuid.UUID]*User{}
)

type User struct {
	pl *player.Player
	h  *world.EntityHandle

	cooldownMap cooldown.MappedCoolDown[PlayerCoolDowns]

	Scoreboard *scoreboard.Scoreboard
	Data       *database.PlayerData
	WTData     WTData
	FirstTime  bool

	FactionInfo FactionInfo
}

type FactionInfo struct {
	DuelInvites           map[*player.Player]bool
	TPARequest            TPARequest
	IgnoreDrilledBlocksAt []mgl64.Vec3
	TeleportTimer         *TeleportTimer
}

type TeleportTimer struct {
	StartPosition mgl64.Vec3
	TargetPosition mgl64.Vec3
	StartTime     time.Time
	CancelFunc    func()
}

type TPARequest struct {
	Target *world.EntityHandle
	To     *world.EntityHandle
}

func newUser(pl *player.Player) (*User, error) {
	if pl == nil {
		panic("New player should not be nil")
	}

	userMu.Lock()
	defer userMu.Unlock()

	ft := false
	d, _ := database.DB.FindPlayer(pl.UUID())
	if d == nil {
		ft = true
		pd := &database.PlayerData{
			UUID:       pl.UUID(),
			Username:   pl.Name(),
			FirstLogin: time.Now(),
			LastLogin:  time.Now(),
			RankId:     database.Player.Shortened(),
			Faction: database.PlayerFaction{
				Stats: database.Stats{
					CrateKeys: map[database.CrateType]int{},
					Kits:      map[database.Rank]time.Time{},
				},
				Home:     map[string]mgl64.Vec3{},
				Backpack: map[int]map[int]database.CustomStack{},
				Bounties: map[uuid.UUID]float64{},
				Role:     database.Member,
			},
		}

		if err := database.DB.CreatePlayer(pd); err != nil {
			return nil, err
		}
		d = pd
	}

	d.ProtocolId = utils.Session(pl).ClientData().GameVersion

	u := &User{
		pl: pl,
		h:  pl.H(),

		cooldownMap: cooldown.NewMappedCoolDown[PlayerCoolDowns](),

		Data:        d,
		FirstTime:   ft,
		FactionInfo: FactionInfo{DuelInvites: map[*player.Player]bool{}},
	}
	users[pl.UUID()] = u
	return u, nil
}

func GetUser(pl *player.Player) *User {
	if users[pl.UUID()] == nil {
		return utils.Panics(newUser(pl))
	}

	userMu.Lock()
	defer userMu.Unlock()

	users[pl.UUID()].pl = pl
	users[pl.UUID()].h = pl.H()

	return users[pl.UUID()]
}

func GetUserByUUID(uuid uuid.UUID) *User {
	userMu.Lock()
	defer userMu.Unlock()

	for _, user := range users {
		if user.Data.UUID == uuid {
			return user
		}
	}
	return nil
}

func Save(pl *player.Player) error {
	user := GetUserByUUID(pl.UUID())
	if user == nil {
		userMu.Lock()
		user = users[pl.UUID()]
		userMu.Unlock()

		if user == nil {
			return utils.PlayerDataNotFoundError{Identifier: pl.UUID().String()}
		}
	}

	return database.DB.SavePlayer(user.Data)
}

func UpdateUserData(pd *database.PlayerData) {
	userMu.Lock()
	defer userMu.Unlock()

	if users[pd.UUID] == nil {
		return
	}
	users[pd.UUID].Data = pd
}

func (u *User) Player() *player.Player {
	return u.pl
}

func (u *User) H() *world.EntityHandle {
	return u.h
}

func (u *User) IsCoolDownActive(cooldownType PlayerCoolDowns, duration time.Duration, renew, create, sendMessage bool) bool {
	coolDown := u.cooldownMap[cooldownType]

	if coolDown == nil {
		coolDown = cooldown.NewCoolDown()
		u.cooldownMap[cooldownType] = coolDown
	}
	exists := coolDown.Active()
	if create && (renew || !coolDown.Active()) {
		coolDown.Set(duration)
	}

	if sendMessage && exists {
		u.pl.Message(text.Colourf(language.Translate(u.pl).Error.CoolDown, coolDown.Remaining().Seconds()))
	}

	return exists
}

func (u *User) CoolDownTimeRemaining(cooldownType PlayerCoolDowns) time.Duration {
	cd := u.cooldownMap[cooldownType]
	if cd == nil {
		return 0
	}
	return cd.Remaining()
}

// StartTeleportTimer starts a 5-second teleport timer that can be cancelled by movement or combat
func (u *User) StartTeleportTimer(targetPos mgl64.Vec3, onComplete func()) {
	// Cancel any existing teleport timer
	u.CancelTeleportTimer()

	startPos := u.pl.Position()
	startTime := time.Now()

	// Create a context that can be cancelled
	done := make(chan bool, 1)

	timer := &TeleportTimer{
		StartPosition:  startPos,
		TargetPosition: targetPos,
		StartTime:      startTime,
		CancelFunc: func() {
			select {
			case done <- true:
			default:
			}
		},
	}

	u.FactionInfo.TeleportTimer = timer

	// Start the timer goroutine
	go func() {
		ticker := time.NewTicker(1 * time.Second)
		defer ticker.Stop()

		for i := 5; i > 0; i-- {
			select {
			case <-done:
				return // Timer was cancelled
			case <-ticker.C:
				// Check if player is still valid before sending message
				if u.pl == nil || u.pl.H() == nil {
					return
				}

				if i > 1 {
					// Show countdown on screen with color-changing numbers
					countdown := i - 1
					var titleText string
					switch countdown {
					case 4:
						titleText = text.Colourf("<green>%d</green>", countdown)
					case 3:
						titleText = text.Colourf("<yellow>%d</yellow>", countdown)
					case 2:
						titleText = text.Colourf("<gold>%d</gold>", countdown)
					case 1:
						titleText = text.Colourf("<red>%d</red>", countdown)
					default:
						titleText = text.Colourf("<white>%d</white>", countdown)
					}

					subtitleText := text.Colourf("<grey>Don't move!</grey>")
					u.pl.SendTitle(title.New(titleText).WithSubtitle(subtitleText))
				} else {
					// Timer completed successfully - execute teleport in player's world context
					u.pl.H().ExecWorld(func(tx *world.Tx, e world.Entity) {
						if player, ok := e.(*player.Player); ok {
							// Double-check timer wasn't cancelled during execution
							if u.FactionInfo.TeleportTimer != nil {
								u.FactionInfo.TeleportTimer = nil
								player.Teleport(targetPos)
								player.SendTitle(title.New(text.Colourf("<green>TELEPORTED</green>")).WithSubtitle(text.Colourf("<grey>Welcome to the wild!</grey>")))
								onComplete()
							}
						}
					})
					return
				}
			}
		}
	}()
}

// CancelTeleportTimer cancels any active teleport timer
func (u *User) CancelTeleportTimer() {
	if u.FactionInfo.TeleportTimer != nil {
		u.FactionInfo.TeleportTimer.CancelFunc()
		u.FactionInfo.TeleportTimer = nil
		// Only send message if player is still valid
		if u.pl != nil {
			u.pl.SendTitle(title.New(text.Colourf("<red>CANCELLED</red>")).WithSubtitle(text.Colourf("<grey>Teleport cancelled!</grey>")))
		}
	}
}

// HasActiveTeleportTimer returns true if the user has an active teleport timer
func (u *User) HasActiveTeleportTimer() bool {
	return u.FactionInfo.TeleportTimer != nil
}

// CheckTeleportMovement checks if the player has moved and cancels teleport if so
func (u *User) CheckTeleportMovement(currentPos mgl64.Vec3) {
	if u.FactionInfo.TeleportTimer == nil {
		return
	}

	// Check if player has moved more than 0.1 blocks from start position
	startPos := u.FactionInfo.TeleportTimer.StartPosition
	if math.Abs(currentPos.X()-startPos.X()) > 0.1 ||
	   math.Abs(currentPos.Y()-startPos.Y()) > 0.1 ||
	   math.Abs(currentPos.Z()-startPos.Z()) > 0.1 {
		u.CancelTeleportTimer()
	}
}

func (u *User) SendScoreboard(numOfSpaces int) {
	seasonText := text.Colourf("<grey>%v</grey>", font.Transform("SEASON 1"))
	u.Scoreboard.Set(0, text.Colourf("%v%v", strings.Repeat(" ", numOfSpaces), seasonText))
	u.pl.SendScoreboard(u.Scoreboard)
}

func (u *User) AddItem(its ...item.Stack) bool {
	if len(u.pl.Inventory().Items())+len(its) > 36 {
		u.pl.Message(text.Colourf(language.Translate(u.pl).Error.InventoryFull))
		return false
	}
	for _, it := range its {
		if _, err := u.pl.Inventory().AddItem(it); err != nil {
			if err := u.pl.Inventory().RemoveItem(it); err != nil {
				panic(err)
			}
			u.pl.Message(text.Colourf(language.Translate(u.pl).Error.InventoryFull))
			return false
		}
	}
	return true
}

type WTData struct {
	Volume struct {
		Pos1 cube.Pos
		Pos2 cube.Pos
	}
	Undo stack.Stack
	Redo stack.Stack
}

func (u *User) Selection() map[cube.Pos]world.Block {
	pos1, pos2 := u.WTData.Volume.Pos1, u.WTData.Volume.Pos2
	selection := map[cube.Pos]world.Block{}
	for x := int(math.Min(float64(pos1.X()), float64(pos2.X()))); x <= int(math.Max(float64(pos1.X()), float64(pos2.X()))); x++ {
		for y := int(math.Min(float64(pos1.Y()), float64(pos2.Y()))); y <= int(math.Max(float64(pos1.Y()), float64(pos2.Y()))); y++ {
			for z := int(math.Min(float64(pos1.Z()), float64(pos2.Z()))); z <= int(math.Max(float64(pos1.Z()), float64(pos2.Z()))); z++ {
				selection[cube.Pos{x - pos1.X(), y - pos1.Y(), z - pos1.Z()}] = u.pl.Tx().Block(cube.Pos{x, y, z})
			}
		}
	}
	return selection
}

// DebouncedSave saves player data after a short delay, canceling previous pending saves
// This prevents spam saving while ensuring data is saved within 3 seconds of changes
func DebouncedSave(pl *player.Player) {
	savesMutex.Lock()
	defer savesMutex.Unlock()

	playerUUID := pl.UUID()

	// Cancel any existing pending save for this player
	if existingTimer, exists := pendingSaves[playerUUID]; exists {
		existingTimer.Stop()
	}

	// Schedule a new save in 3 seconds
	pendingSaves[playerUUID] = time.AfterFunc(3*time.Second, func() {
		// Save both Dragonfly and custom player data
		if err := server.DragonflyConfig.PlayerProvider.Save(pl.UUID(), pl.Data(), server.MCServer.World()); err != nil {
			errorCode := utils.RandString(6)
			slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save Dragonfly player data (debounced): " + err.Error())
		}

		if err := Save(pl); err != nil {
			errorCode := utils.RandString(6)
			slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data (debounced): " + err.Error())
		}

		// Remove from pending saves
		savesMutex.Lock()
		delete(pendingSaves, playerUUID)
		savesMutex.Unlock()
	})
}

// SavePlayerDataImmediate saves player data immediately for critical actions
// Use sparingly - only for very important actions like bank note usage, faction changes, etc.
func SavePlayerDataImmediate(pl *player.Player) {
	go func() {
		// Cancel any pending debounced save since we're saving now
		savesMutex.Lock()
		if existingTimer, exists := pendingSaves[pl.UUID()]; exists {
			existingTimer.Stop()
			delete(pendingSaves, pl.UUID())
		}
		savesMutex.Unlock()

		// Save both Dragonfly and custom player data immediately
		if err := server.DragonflyConfig.PlayerProvider.Save(pl.UUID(), pl.Data(), server.MCServer.World()); err != nil {
			errorCode := utils.RandString(6)
			slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save Dragonfly player data immediately: " + err.Error())
		}

		if err := Save(pl); err != nil {
			errorCode := utils.RandString(6)
			slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data immediately: " + err.Error())
		}
	}()
}
