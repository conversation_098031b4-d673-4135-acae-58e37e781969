package schedulers

import (
	"log/slog"
	"os"
	"server/server/database"
	"server/server/utils"
)

func InitMongo(log *slog.Logger) {
	if conn := os.Getenv("DATABASE_URI"); conn != "" { //"mongodb://localhost:27017" OR "mongodb://mongo:27017"
		log.Info("Connecting to MongoDB...!")
		database.DB = utils.Panics(database.NewMongoDBDatabase(conn))
	} else {
		database.DB = database.NewLocalDatabase()
	}

	log.Info("Successfully connected to the database!", "type", database.DB.Type())
}
