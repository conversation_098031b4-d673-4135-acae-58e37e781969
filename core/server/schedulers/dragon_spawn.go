package schedulers

import (
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"math/rand"
	core "server/server"
	"server/server/entity/mobs/boss"
	"server/server/language"
	"time"
)

func ScheduleDragonSpawning() {
	for {
		time.Sleep(15 * time.Second)
		x := float64(-4500 + rand.Intn(9000))
		z := float64(-4500 + rand.Intn(9000))
		core.MCServer.World().Exec(func(tx *world.Tx) {
			y := float64(tx.HighestBlock(int(x), int(z)) + 20)
			v := mgl64.Vec3{x, y, z}
			dragons := []func(){
				func() { boss.NewDarkDragon(v, tx) },
				func() { boss.NewEarthDragon(v, tx) },
				func() { boss.NewFireDragon(v, tx) },
				func() { boss.NewIceDragon(v, tx) },
				func() { boss.NewLightDragon(v, tx) },
				func() { boss.NewWaterDragon(v, tx) },
			}
			dragons[rand.Intn(len(dragons))]()
			for pl := range core.MCServer.Players(tx) {
				pl.Message(text.Colourf("<red>*******************************</red>"))
				pl.Message("")
				pl.Message(text.Colourf(language.Translate(pl).DragonSpawn, v))
				pl.Message("")
				pl.Message(text.Colourf("<red>*******************************</red>"))
			}
		})
		time.Sleep(15 * time.Minute)
	}
}
