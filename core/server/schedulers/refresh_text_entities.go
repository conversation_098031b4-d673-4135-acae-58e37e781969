package schedulers

import (
	"context"
	"github.com/df-mc/dragonfly/server"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/dragonfly/server/world/particle"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	core "server/server"
	"server/server/database"
	"server/server/utils"
	"strings"
	"time"
)

func RefreshTextEntities() {
	textMap := map[string][]interface{}{
		//"faction.stats.kills":      {"player_data", mgl64.Vec3{}, "<bold><gold>MOST KILLS LEADERBOARD</gold></bold>", "Faction.Stats.Kills"},
		//"faction.stats.deaths":     {"player_data", mgl64.Vec3{}, "<bold><gold>MOST DEATHS LEADERBOARD</gold></bold>", "Faction.Stats.Deaths"},
		//"faction.stats.killstreak": {"player_data", mgl64.Vec3{}, "<bold><gold>HIGHEST KILL-STREAK LEADERBOARD</gold></bold>", "Faction.Stats.KillStreak"},
		"faction.stats.doubloons": {"player_data", core.Config.Hub.Leaderboard.PlayerRich, "<bold><gold>RICHEST PLAYERS LEADERBOARD</gold></bold>", "Faction.Stats.Doubloons"},
		"faction.stats.strength":  {"player_data", core.Config.Hub.Leaderboard.PlayerStrength, "<bold><gold>STRONGEST PLAYERS LEADERBOARD</gold></bold>", "Faction.Stats.Strength"},
		"bankdoubloons":           {"faction_data", core.Config.Hub.Leaderboard.FactionRich, "<bold><gold>RICHEST FACTIONS LEADERBOARD</gold></bold>", "BankDoubloons"},
		"strength":                {"faction_data", core.Config.Hub.Leaderboard.FactionStrength, "<bold><gold>STRONGEST FACTIONS LEADERBOARD</gold></bold>", "Strength"},
	}

	// Remove all text entities
	core.MCServer.World().Exec(func(tx *world.Tx) {
		for _, info := range textMap {
			tx.Block(cube.PosFromVec3(info[1].(mgl64.Vec3)))
		}
		for t := range core.Config.Hub.Crates {
			pos := core.Config.Hub.Crates[t]
			tx.Block(cube.PosFromVec3(pos))
		}

		for e := range tx.Entities() {
			if e.H().Type() == entity.TextType {
				utils.Panic(e.Close())
			}
		}
	})

	// Update LeaderBoards
	if client := database.DB.Client(); client != nil {
		go func() {
			updateLeaderBoards(core.MCServer, client, textMap)
			for range time.NewTicker(30 * time.Second).C {
				updateLeaderBoards(core.MCServer, client, textMap)
			}
		}()
	}

	// Update Crates NameTags
	for t := range core.Config.Hub.Crates {
		p := core.Config.Hub.Crates[t]
		core.MCServer.World().Exec(func(tx *world.Tx) {
			tx.AddEntity(entity.NewText("-", p.Add(mgl64.Vec3{0, 1.25, 0})))
		})
		go func() {
			for {
				core.MCServer.World().Exec(func(tx *world.Tx) {
					tx.AddParticle(p, particle.Lava{})
				})
				time.Sleep(50 * time.Millisecond)
			}
		}()
	}
}

func updateLeaderBoards(srv *server.Server, client *mongo.Client, textMap map[string][]interface{}) {
	for key, info := range textMap {
		col := info[0].(string)
		vec := info[1].(mgl64.Vec3)
		title := info[2].(string)
		access := info[3].(string)

		res, err := client.Database("mmc").Collection(col).Find(context.TODO(), bson.M{key: bson.M{"$exists": true}}, options.Find().SetSort(bson.M{key: -1}).SetLimit(10))
		if err != nil {
			panic(err.Error())
		}

		var pds []*database.PlayerData
		var fds []*database.FactionData
		if col == "player_data" {
			err = res.All(context.TODO(), &pds)
		} else {
			err = res.All(context.TODO(), &fds)
		}

		if err != nil {
			panic(err.Error())
		}

		str := text.Colourf("%v\n", title)
		for i := 0; i < max(len(pds), len(fds)); i++ {
			var d interface{}
			if len(pds) > 0 {
				d = pds[i]
			} else {
				d = fds[i]
			}

			var name string
			if pd, ok := d.(*database.PlayerData); ok {
				// For player leaderboards, show only username in aqua color
				name = text.Colourf("<aqua>%v</aqua>", pd.Username)
			} else if fd, ok := d.(*database.FactionData); ok {
				// For faction leaderboards, show faction name
				name = fd.Name
			}

			// Get position color based on rank
			var positionColor string
			switch i + 1 {
			case 1:
				positionColor = "<gold>"
			case 2:
				positionColor = "<yellow>"
			case 3:
				positionColor = "<dark-aqua>"
			default:
				positionColor = "<grey>"
			}

			// Get the amount and format it with commas
			amount := utils.GetNestedField(d, strings.Split(access, ".")).(float64)
			var formattedAmount string

			// Check if this is a richest leaderboard (doubloons/bankdoubloons)
			if strings.Contains(access, "Doubloons") || key == "bankdoubloons" {
				// For richest leaderboards, always show full numbers with commas
				formattedAmount = utils.AddCommas(amount)
			} else {
				// For other leaderboards (strength, kills, etc.), use shortened format
				if amount >= 1000 {
					formattedAmount = utils.ShortenNumber(amount, 0)
				} else {
					formattedAmount = utils.AddCommas(amount)
				}
			}

			str += text.Colourf(
				"%s%v</%s>. <aqua>%v</aqua> <grey>-</grey> <green>%v</green>\n",
				positionColor,
				i+1,
				strings.TrimPrefix(strings.TrimSuffix(positionColor, ">"), "<"),
				name,
				formattedAmount,
			)
		}

		if vec != (mgl64.Vec3{}) {
			txtEntity := core.TextEntities[key]
			srv.World().Exec(func(tx *world.Tx) {
				if txtEntity == nil {
					e := entity.NewText(str, vec)
					tx.AddEntity(e)
					core.TextEntities[key] = e
				} else {
					if e, ok := txtEntity.Entity(tx); ok {
						e.(*entity.Ent).SetNameTag(str)
					}
				}
			})
		}
	}
}
