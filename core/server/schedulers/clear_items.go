package schedulers

import (
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	core "server/server"
	"server/server/language"
	"time"
)

func ScheduleItemsClearing() {
	for {
		time.Sleep(10 * time.Minute)
		<-core.MCServer.World().Exec(func(tx *world.Tx) {
			for pl := range core.MCServer.Players(tx) {
				pl.Message(text.Colourf(language.Translate(pl).ItemClearCooldown, core.Config.Prefix, "1 minute"))
			}
		})
		time.Sleep(1 * time.Minute)
		countdown := []string{"<dark-green>5</dark-green>", "<green>4</green>", "<yellow>3</yellow>", "<gold>2</gold>", "<red>1</red>"}
		for _, cd := range countdown {
			<-core.MCServer.World().Exec(func(tx *world.Tx) {
				for pl := range core.MCServer.Players(tx) {
					pl.Message(text.Colourf(language.Translate(pl).ItemClearCooldown, core.Config.Prefix, cd))
				}
			})
			time.Sleep(1 * time.Second)
		}
		core.MCServer.World().Exec(func(tx *world.Tx) {
			var numOfIts int
			for e := range tx.Entities() {
				if ent, ok := e.(*entity.Ent); ok {
					if ent.H().Type() == entity.ItemType {
						if err := ent.Close(); err != nil {
							panic(err.Error())
						}
						numOfIts++
					}
				}
			}

			for pl := range core.MCServer.Players(tx) {
				pl.Message(text.Colourf(language.Translate(pl).ItemsCleared, core.Config.Prefix, numOfIts))
			}
		})
	}
}
